package com.lumlux.signal.entity;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <p>Title: PutTimePlanDetailBean.java</p>
 * <p>Description: PutTimePlanDetailBean单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2025/7/5</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class PutTimePlanDetailBean {



    @NotEmpty(message = "方案号不能为空")
    @Min(value = 0, message = "方案号必须大于等于0")
    @Max(value = 255, message = "方案号不能大于255")
    private String schemeNo;
      @NotEmpty(message = "方案开始时间不能为空")
    private String schemeStartTime;
    @NotEmpty(message = "方案结束时间不能为空")
    private String schemeEndTime;
    @NotEmpty(message = "使能状态不能为空")
    private String enabled ;

    public String getSchemeStartTime() {
        return schemeStartTime;
    }

    public void setSchemeStartTime(String schemeStartTime) {
        this.schemeStartTime = schemeStartTime;
    }

    public String getSchemeEndTime() {
        return schemeEndTime;
    }

    public void setSchemeEndTime(String schemeEndTime) {
        this.schemeEndTime = schemeEndTime;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getSchemeNo() {
        return schemeNo;
    }

    public void setSchemeNo(String schemeNo) {
        this.schemeNo = schemeNo;
    }
}
