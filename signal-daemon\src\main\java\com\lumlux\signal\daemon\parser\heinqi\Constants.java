package com.lumlux.signal.daemon.parser.heinqi;

public interface Constants {

	public byte HEAD = 0x6b;

	public interface PACKET_TYPE {
		// byte SIGN_IN = 0x01;
		// byte SIGN_OUT = 0x02;
		byte SESSION_INFO = 0x04;
		// byte CMD_GET = 0x08;
		byte GET_DATA_REQUEST = 0x10;
		byte GET_LIMIT_REQUEST = 0x12;
		byte PUT_LIMIT_REQUEST = 0x14;
		byte PUT_START_REQUEST = 0x16;
		byte PUT_TIME_REQUEST = 0x18;
		byte PUT_CARD_REQUEST = 0x1a;
		byte GET_NEW_DATA_REQUEST = 0x1C;
		byte PUT_CONTROL_REQUEST = 0x1E;
		byte GET_VERSION_REQUEST = 0x30;
		byte PUT_CONFIG_REQUEST = 0x32;
		byte GET_CONFIG_REQUEST = 0x34;
		byte PUT_TIME_CONTROL_REQUEST = 0x40;
		byte PUT_TIME_CONTROL_ENBALED_REQUEST = 0x44;

		byte INFORM_REQUEST = 0x20;
		byte GET_TIME_REQUEST = 0x22;

		byte PUT_VARIABLE_CONFIG_REQUEST = 0x24;
		byte GET_VARIABLE_CONFIG_REQUEST = 0x26;

		byte SECOND_PUT_LIMIT_REQUEST = 0x54;
		byte SECOND_PUT_PARAMETER_REQUEST = 0x58;
		byte SECOND_PUT_DEViICE_RESTART = 0x59;
		byte SECOND_PUT_COLUMNCONFIG_RESTART = (byte) 0xC5;
		byte SECOND_PUT_RED_RESTART = (byte) 0xC6;
		byte SECOND_PUT_GREEN_RESTART = (byte) 0xC7;
		byte SECOND_PUT_HRON_RESTART = (byte) 0xC8;
		byte SECOND_PUT_DISPLAY_RESTART = (byte) 0xC9;
		byte SECOND_GET_PARAMETER_REQUEST = 0x5A;
		byte SECOND_GET_REFERENCE_REQUEST = 0x5C;
		byte SECOND_PUT_START_REQUEST = 0x5E;
		byte SECOND_PUT_RESET_LIMIT_REQUEST = 0x60;

		byte PUT_CONFIG_REQUEST_DIMMING = 0x62;

		byte PUT_CONFIG_REQUEST_STRAGY = 0x64;

		byte PUT_CONFIG_YELLOW_LIGTH = 0x66;

		byte PUT_CONFIG_REQUEST_STRAGY_TEST = 0x1d;

		byte PUT_CONFIG_CABINET = 0x57;

		byte PUT_CAMERA_COME_PERSON = 0x70;

		byte PUT_CONFIG_COME_PERSON_TEST =  (byte)0xa2;

		byte PUT_ADHOC_SCHEME_CONFIG = -79;

		byte PUT_ADHOC_TIME_PLAN_CONFIG =  -78;

       byte PUT_PORTABLE_TIME_PLAN_CONFIG =  -71;
		byte PUT_ADHOC_HEART_BEAT = -95;
		byte PUT_ADHOC_QUERY_INFO = -94;
		byte PUT_ADHOC_FOLLOW_CONTROLLER_INFO =-92;
		byte PUT_MAC_INFO =-91;
		byte PUT_DELETE_ADHOC_LIGHTINFO = -90;

		byte PUT_ENDNETWORK_INFO = -89;

		byte PUT_ENDSCANNER_INFO =-88;

		byte PUT_MAGNETICLOCK_INFO =0x69;

		byte PUT_PORTABLE_TRAFFIC_QUERY_INFO =-86;
		byte PUT_PORTABLE_TRAFFIC_MAC_INFO =-85;

		byte PUT_PORTABLE_TRAFFIC_SCHEME_CONFIG = -76;

		byte PUT_PORTABLE_SCHEME_LEVEL_INFO = -75;

		byte PUT_DIMMING_PORTABLE_CONFIG =-74;

		byte PUT_ONCLICK_PARAMETER =-72;

		byte PUT_ADHOC_PORTABLE_SCHEME_LEVEL_INFO = 0x5b;

		byte PUT_QUERY_ADHOC_INFO = -84;

		byte PUT_STARTNETWORK_INFO =122;


	}
}
