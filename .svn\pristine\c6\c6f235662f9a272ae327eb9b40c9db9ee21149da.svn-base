package com.lumlux.signal.controller;


import com.heinqi.yangtes.base.web.AsyncResponseData;

import com.heinqi.yangtes.jee.commons.web.BaseController;
import com.lumlux.commons.entity.TblAdHOCScheme;
import com.lumlux.commons.entity.TblAdHOCTimePlan;
import com.lumlux.commons.entity.TblCollector;
import com.lumlux.signal.entity.PutTimePlanBean;

import com.lumlux.signal.entity.PutTimePlanDetailBean;
import com.lumlux.signal.service.DaemonService;
import com.lumlux.signal.service.TblAdHOCSchemeService;
import com.lumlux.signal.service.TblAdHOCTimePlanService;
import com.lumlux.signal.service.TblCollectorService;
import com.lumlux.signal.util.Constant;
import com.lumlux.signal.util.ValidateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.*;

/**
 * <p>Title: TblAdHOCTimePlanController.java</p>
 * <p>Description: TblAdHOCTimePlanController单表表单</p>
 * <p>Author: Ma Jian</p>
 * <p>Date: 2024/5/30</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
@RestController
@RequestMapping(value = "${adminPath}/signal/adHOCTimePlan")
//@Validated
public class TblAdHOCTimePlanController extends BaseController {



    @Autowired
    private TblCollectorService tblCollectorService;


    @Autowired
    private TblAdHOCSchemeService adHOCSchemeService;
    @Autowired
    private TblAdHOCTimePlanService tblAdHOCTimePlanService;




    @Autowired
    private DaemonService daemonService;


    //    @RequiresPermissions("signal:adHOCTimePlan:getTimePlan")
    @RequestMapping(value = "getTimePlan")
    public AsyncResponseData.ResultData getTimePlan(@RequestParam("id") String id)
    {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();
        if (id ==null ) {
            messages.put("message", "参数信息不全");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

        TblCollector collector = tblCollectorService.get(id);
        if (null == collector) {
            messages.put("message", "该集中器不存在");
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            data.setMessages(messages);
            return data;
        }

        List<TblAdHOCTimePlan> tblAdHOCTimePlans = tblAdHOCTimePlanService.selectAllTimePlan(id);
        messages.put("message", "该时间计划查询成功");
        data.setMessages(messages);
        data.setData(tblAdHOCTimePlans);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;
    }

//    @RequiresPermissions("signal:adHOCTimePlan:putTimePlan")
    @RequestMapping(value = "putTimePlan")
    public AsyncResponseData.ResultData putTimePlanInfo(@RequestBody PutTimePlanBean timePlanBean) {

        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        Set<ConstraintViolation<PutTimePlanBean>> result = ValidateUtil.validate(timePlanBean);
        // 抛出检验异常
        if (result.size() > 0) {
            Iterator<ConstraintViolation<PutTimePlanBean>> it = result.iterator();

            while (it.hasNext()) {
                ConstraintViolation<PutTimePlanBean> str = it.next();
//                System.out.println(str.getMessage() + "\n");
                messages.put("message", str.getMessage());
                data.setMessages(messages);
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                return data;
            }
        }
        TblCollector collector = tblCollectorService.get(timePlanBean.getId());
        if (collector == null) {
            messages.put("message", "该集中器不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }
        List<PutTimePlanDetailBean> planDetailList = timePlanBean.getPlanDetailList();
        if (planDetailList == null || planDetailList.size() == 0){
            messages.put("message", "该计划方案明细不能为空");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }
        for (PutTimePlanDetailBean planDetailBean : planDetailList) {
            String schemeNo = planDetailBean.getSchemeNo();
            boolean b = Constant.SCHEME_NUNBER_STATUS.HUANG_SHAN_SCHEME ==Integer.parseInt(schemeNo) || Constant.SCHEME_NUNBER_STATUS.TOTAL_TURNOFF_SCHEME ==Integer.parseInt(schemeNo);
            if ( !b  &&  tblAdHOCTimePlanService.selectTimePlanByPlanNoAndCollectorId(timePlanBean.getId(), timePlanBean.getControlType(), timePlanBean.getPlanNo(), planDetailBean.getSchemeNo()) == null) {
                messages.put("message", "该计划号不存在");
                data.setMessages(messages);
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                return data;
            }
        }
         /*   String schemeNo = timePlanBean.getSchemeNo();
        boolean b = Constant.SCHEME_NUNBER_STATUS.HUANG_SHAN_SCHEME ==Integer.parseInt(schemeNo) || Constant.SCHEME_NUNBER_STATUS.TOTAL_TURNOFF_SCHEME ==Integer.parseInt(schemeNo);
        if ( !b  &&  tblAdHOCTimePlanService.selectTimePlanByPlanNoAndCollectorId(timePlanBean.getId(), timePlanBean.getControlType(), timePlanBean.getPlanNo(), timePlanBean.getSchemeNo()) == null) {
                messages.put("message", "该计划号不存在");
                data.setMessages(messages);
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                return data;
            }*/
        int count = tblCollectorService.selectCount(timePlanBean.getId());
        try {
            //下放
            tblAdHOCTimePlanService.putTimePlanInfo(count,collector.getCode(),timePlanBean,collector.getModel());//下放计时
            Thread.sleep(3000);
            ExecutorService executor = Executors.newSingleThreadExecutor();
            Future<String> future = executor.submit(new Callable<String>() {
                @Override
                public String call() throws Exception {
                    String putFlag = tblCollectorService.getPutFlag(collector.getId());
                    if (putFlag != null) {
                        return putFlag;
                    }
                    return "0";
                }
            });
            count++;

            if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCount(count,timePlanBean.getId());

            tblAdHOCTimePlanService.updateTimePlan(timePlanBean);


            String putFlagResult = future.get();
            if (putFlagResult != null) {
                if (!"1".equals(putFlagResult)) {
                    messages.put("message", "自组网或者移动信号灯下放时间计划失败");
                    data.setMessages(messages);
                    data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                    return data;
                } else {
                    messages.put("message", "自组网或者移动信号灯下放时间计划成功") ;
                    data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                    data.setMessages(messages);
                    return data;

                }
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        return null;
    }


//    @RequiresPermissions("signal:adHOCTimePlan:addTimePlan")
    @RequestMapping(value = "addTimePlan")
    public AsyncResponseData.ResultData addTimePlanInfo( @RequestBody PutTimePlanBean timePlanBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        Set<ConstraintViolation<PutTimePlanBean>> result = ValidateUtil.validate(timePlanBean);
        // 抛出检验异常
        if (result.size() > 0) {
            Iterator<ConstraintViolation<PutTimePlanBean>> it = result.iterator();

            while (it.hasNext()) {
                ConstraintViolation<PutTimePlanBean> str = it.next();
//                System.out.println(str.getMessage() + "\n");
                messages.put("message", str.getMessage());
                data.setMessages(messages);
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                return data;
            }
        }

        TblCollector collector = tblCollectorService.get(timePlanBean.getId());
        if (collector == null) {
            messages.put("message", "该集中器不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }

/*        if (tblAdHOCTimePlanService.selectTimePlanByPlanNoAndCollectorId(timePlanBean.getId(),timePlanBean.getControlType(),timePlanBean.getPlanNo(),timePlanBean.getSchemeNo() )!=null) {
            messages.put("message", "该计划号已存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }*/

//        TblAdHOCScheme schemeInfo = adHOCSchemeService.getSchemeInfo(collector.getId(), timePlanBean.getSchemeNo()).get(0);
//        boolean specialScheme = Constant.SCHEME_NUNBER_STATUS.HUANG_SHAN_SCHEME == Integer.parseInt(timePlanBean.getSchemeNo()) || Constant.SCHEME_NUNBER_STATUS.TOTAL_TURNOFF_SCHEME == Integer.parseInt(timePlanBean.getSchemeNo());
    /*    if (schemeInfo ==null && !specialScheme ){
            messages.put("message", "该方案不存在，请创建方案");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
       }*/

        tblAdHOCTimePlanService.insertTimePlan(timePlanBean);
        messages.put("message", "自组网时间计划信息添加成功");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;

    }

    //    @RequiresPermissions("signal:adHOCTimePlan:addTimePlan")
    @RequestMapping(value = "updateTimePlan")
    public AsyncResponseData.ResultData updateTimePlan(@RequestBody PutTimePlanBean timePlanBean) {

        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        Set<ConstraintViolation<PutTimePlanBean>> result = ValidateUtil.validate(timePlanBean);
        // 抛出检验异常
        if (result.size() > 0) {
            Iterator<ConstraintViolation<PutTimePlanBean>> it = result.iterator();

            while (it.hasNext()) {
                ConstraintViolation<PutTimePlanBean> str = it.next();
//                System.out.println(str.getMessage() + "\n");
                messages.put("message", str.getMessage());
                data.setMessages(messages);
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                return data;
            }
        }


        TblCollector collector = tblCollectorService.get(timePlanBean.getId());
        if (collector == null) {
            messages.put("message", "该集中器不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

            if( tblAdHOCTimePlanService.selectTimePlanTotal(timePlanBean.getId(),timePlanBean.getPlanNo())== 0 ){
                messages.put("message", "该计划号不存在");
                data.setMessages(messages);
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                return data;

        }
        List<PutTimePlanDetailBean> planDetailList = timePlanBean.getPlanDetailList();
            if (planDetailList != null || planDetailList.size() >= 0) {
             for (PutTimePlanDetailBean detailBean : planDetailList) {
                 TblAdHOCScheme schemeInfo = adHOCSchemeService.getSchemeInfo(collector.getId(), detailBean.getSchemeNo()).get(0);
                 boolean specialScheme = Constant.SCHEME_NUNBER_STATUS.HUANG_SHAN_SCHEME == Integer.parseInt(detailBean.getSchemeNo()) || Constant.SCHEME_NUNBER_STATUS.TOTAL_TURNOFF_SCHEME == Integer.parseInt(detailBean.getSchemeNo());
                 if (schemeInfo ==null && !specialScheme ){
                     messages.put("message", "该方案不存在，请创建方案");
                     data.setMessages(messages);
                     data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                     return data;
                 }

                 tblAdHOCTimePlanService.updateTimePlan(timePlanBean);

             }
            }
     /*   TblAdHOCScheme schemeInfo = adHOCSchemeService.getSchemeInfo(collector.getId(), timePlanBean.getSchemeNo()).get(0);
        boolean specialScheme = Constant.SCHEME_NUNBER_STATUS.HUANG_SHAN_SCHEME == Integer.parseInt(timePlanBean.getSchemeNo()) || Constant.SCHEME_NUNBER_STATUS.TOTAL_TURNOFF_SCHEME == Integer.parseInt(timePlanBean.getSchemeNo());
        if (schemeInfo ==null && !specialScheme ){
            messages.put("message", "该方案不存在，请创建方案");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }*/




        messages.put("message", "自组网时间计划信息修改成功");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;
    }
    //    @RequiresPermissions("signal:adHOCTimePlan:deleteTimePlan")
    @RequestMapping(value = "deleteTimePlan")
    public AsyncResponseData.ResultData deleteTimePlan(@RequestParam("collectorId")String collectorId,@RequestParam("controlType")String controlType,  @RequestParam("planNo")String planNo,  @RequestParam("schemeNo")String schemeNo){

        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        if (collectorId==null||planNo== null ||controlType==null||schemeNo==null) {
            messages.put("message", "参数信息不全");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

        TblAdHOCTimePlan tblAdHOCTimePlan = tblAdHOCTimePlanService.selectTimePlanByPlanNoAndCollectorId(collectorId,controlType,planNo,schemeNo);
        if(tblAdHOCTimePlan==null){
            messages.put("message", "该计划号不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

        tblAdHOCTimePlanService.deleteTimePlan(collectorId,controlType,planNo,schemeNo);
        messages.put("message", "自组网时间计划信息删除成功");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;
    }

    /**
     * 是否启用灯状态
     * @param collectorId
     * @param planNo
     * @param enabled
     * @return
     */

    //    @RequiresPermissions("signal:adHOCTimePlan:enableTimePlan")

    @RequestMapping(value = "enableTimePlan")
    public AsyncResponseData.ResultData updateEnableTimePlan(@RequestParam("collectorId") String collectorId,@RequestParam("controlType")String controlType,@RequestParam("planNo")String planNo,@RequestParam("schemeNo")String schemeNo,@RequestParam("enabled") String enabled)
    {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();
        if (collectorId==null||planNo== null || enabled ==null) {
            messages.put("message", "参数信息不全");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }
        TblAdHOCTimePlan tblAdHOCTimePlan = tblAdHOCTimePlanService.selectTimePlanByPlanNoAndCollectorId(collectorId,controlType,planNo,schemeNo);
        if(tblAdHOCTimePlan==null){
            messages.put("message", "该计划号不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

        tblAdHOCTimePlanService.updateEnableTimePlan(collectorId,controlType,planNo,schemeNo,enabled);
        messages.put("message", "该灯使能状态已更新");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;

    }
}
