package com.lumlux.signal.entity;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * <p>Title: PutTimePlanBean.java</p>
 * <p>Description: TimePlanBean单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2024/5/30</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class PutTimePlanBean {

    @NotEmpty(message = "集中器id不能为空")
    private String id;


    @NotEmpty(message = "星期号不能为空")
    @Min(value = 1, message = "星期信息必须大于等于1")
    @Max(value = 7, message = "星期信息不能大于7")
    private String controlType;

    @NotEmpty(message = "计划号不能为空")
    @Min(value = 1, message = "计划号必须大于等于1")
    private String planNo;

    private List<PutTimePlanDetailBean> planDetailList;
  /*  @NotEmpty(message = "方案开始时间不能为空")
    private String schemeStartTime;
    @NotEmpty(message = "方案结束时间不能为空")
    private String schemeEndTime;
    @NotEmpty(message = "使能状态不能为空")
    private String enabled ;*/

    public List<PutTimePlanDetailBean> getPlanDetailList() {
        return planDetailList;
    }

    public void setPlanDetailList(List<PutTimePlanDetailBean> planDetailList) {
        this.planDetailList = planDetailList;
    }

    public String getControlType() {
        return controlType;
    }

    public void setControlType(String controlType) {
        this.controlType = controlType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

   /* public String getSchemeNo() {
        return schemeNo;
    }

    public void setSchemeNo(String schemeNo) {
        this.schemeNo = schemeNo;
    }*/

    public String getPlanNo() {
        return planNo;
    }

    public void setPlanNo(String planNo) {
        this.planNo = planNo;
    }



    /*    public List<TimePlanDetailBean> getTimePlanDetailBeans() {
        return timePlanDetailBeans;
    }

    public void setTimePlanDetailBeans(List<TimePlanDetailBean> timePlanDetailBeans) {
        this.timePlanDetailBeans = timePlanDetailBeans;
    }*/

}
