<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld"%>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld"%>

<script src="/adHoc/resources/data/js/vue.js"></script>
<link rel="stylesheet" type="text/css" href="/adHoc/resources/data/css/element-ui.css"/>
<script src="/adHoc/resources/data/js/element-ui.js"></script>

<link rel="stylesheet" href="/adHoc/resources/vueComponent/signalTimingSlider/signalTimingSlider.css">
<link rel="stylesheet" href="/adHoc/resources/vueComponent/sketchMap/sketchMap.css">
<script src="/adHoc/resources/vueComponent/utils.js"></script>
<script src="/adHoc/resources/vueComponent/signalTimingSlider/signalTimingSlider.js"></script>
<script src="/adHoc/resources/vueComponent/sketchMap/sketchMap.js"></script>

<div class="flexWrapper">
    <layout:default title="信号机-详细配置">
        <ul class="nav nav-tabs" style="margin-bottom: 0px;">
            <li><a href="${ctx}/signal/tblCollector/list?condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}&condition.model=15,16">信号机列表</a></li>
            <li><a href="#" onclick="editAdHoc()">修改信号机</a></li>
            <li class="active"><a href="#">详细配置</a></li>
        </ul>
        <form id="searchForm" style="display: none">
            <input id="dataEntityId" name="dataEntity.id" value=""/>
            <input id="conditionModel" name="condition.model" value="15"/>
        </form>
        <script>
            function editAdHoc(){
                $("#dataEntityId").val(new URLSearchParams(window.location.search).get('id'));
                $("#searchForm").attr("action","${ctx}/signal/tblCollector/form");
                $("#searchForm").submit();
                return false;
            }

            function clickOpenClose(leftIsShown) {
                if(parent.$('#left').css('display') == (leftIsShown ? 'block' : 'none'))
                    parent.$("#openClose").click();
            }

            window.addEventListener('beforeunload', _ => {
                clickOpenClose(false)
            })
        </script>
    </layout:default>

    <div id="adHocDetailConfig" class="adHocDetailConfigWrapper" v-cloak>
        <div class="sketchMapDiv">
            <div class="panelBreadcrumb">
                信号机 > {{adHocControllrtInfo.name}} > {{tabPanelLabels[activeTab]}}
            </div>
            <sketch-map ref="sketchMap" :sketch-map-image="sketchMapImage"
                :component-library-title="'信号灯类型'" :component-library="lightTypeDict" 
                :marked-components="displaiedSignals">
                <template slot="markedComponent" slot-scope="scope">
                    <div class="markedComponentWrapper">
                        <img draggable="false" :src="'${ctxStatic}/image/adHoc/sketchMapIcon/' + scope.component.remarks + '.png'">
                    </div>
                </template>
                <template slot="libraryComponent" slot-scope="scope">
                    <div class="libraryComponentWrapper">
                        <img draggable="false" :src="'${ctxStatic}/image/adHoc/sketchMapIcon/' + scope.component.remarks + '.png'">
                    </div>
                </template>
            </sketch-map>
        </div>
        <el-tabs v-model="activeTab" tab-position="right" type="border-card" class="configTabs" closable
                 @tab-click="onTabClick" @tab-remove="onTabRemoveBtnClick">
            <el-tab-pane :label="tabPanelLabels['netWorkTab']" name="netWorkTab" style="padding: 0px;">
                <div class="scanPanel">
                    <div class="scanStartBtn" :class="scanStatus ? 'scanningBtn' : ''" v-if="scanStatus < 2" @click="onScanBtnClick">
                        {{scanStatus ? '正在' : '开始'}}扫描
                    </div>
                    <div v-else class="scanResultTableWrapper">
                        <el-table :data="scanResultTableData" :border="scanResultTableData.length > 0" stripe :default-sort="{prop: 'lightNumber', order: 'ascending'}">
                            <el-table-column sortable :align="'center'" prop="lightNumber" label="灯号" width="70">
                            </el-table-column>
                            <el-table-column sortable :align="'center'" prop="address" label="MAC地址" width="120">
                                <template slot-scope="scope">
                                    <div class="rtlWrapper">
                                        <span :title="scope.row.address">
                                            {{scope.row.address}}
                                        </span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column sortable :align="'center'" prop="lightType" label="灯类型" width="85">
                                <template slot-scope="scope">
                                    {{lightTypeDict.find(e => e.value == scope.row.lightType).label}}
                                </template>
                            </el-table-column>
                            <el-table-column sortable :align="'center'" prop="rssi" label="RSSI" width="80">
                            </el-table-column>
                            <el-table-column sortable :align="'center'" label="GPS" width="80">
                                <template slot-scope="scope">
                                    {{+scope.row.gpsStatus ? '异常' : '正常'}}
                                </template>
                            </el-table-column>
                            <el-table-column sortable :align="'center'" prop="controller" label="组网状态" width="100">
                                <template slot-scope="scope">
                                    <el-link v-if="getNetworkStatus(scope.row).type != 'default'" :type="getNetworkStatus(scope.row).type" 
                                        :title="getNetworkStatus(scope.row, true).text" @click="onNetworkNodeNetworkStatusLinkClick(scope.row)">
                                        {{getNetworkStatus(scope.row).text}}
                                    </el-link>
                                    <span v-else :title="getNetworkStatus(scope.row).text">
                                        {{getNetworkStatus(scope.row).text}}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column sortable :align="'center'" prop="updateTime" label="更新时间" width="100">
                                <template slot-scope="scope">
                                    <span :title="scope.row.updateDate">
                                        {{formatDate(scope.row.updateDate)}}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column fixed="right" :align="'center'" label="操作" width="120">
                                <template slot-scope="scope">
                                    <div class="scanTableBtnGroup">
                                        <i class="el-icon-aim" :class="scope.row.isActive ? 'isActive' : ''" title="亮灯寻址" @click="onNetworkNodeSearchLightBtnClick(scope.row)"></i>
                                        <i class="el-icon-add-location" title="地图标点" @click="onNetworkNodeLocationBtnClick(scope.row)"></i>
                                        <%-- <i class="el-icon-delete-location" title="删除标点" v-else></i> --%>
                                        <i class="el-icon-edit" title="编辑" @click="onNetworkNodeConfigBtnClick(scope.row)"></i>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="schemesTimingDiv">
                            <el-button type="success" :loading="btnLoadingStatus.networkComplete" @click="onNetworkCompleteBtnClick">组网完毕</el-button>
                            <el-button v-if="scanStatus > 1" type="danger" :loading="btnLoadingStatus.stopScan" @click="onStopScanBtnClick">停止扫描</el-button>
                            <el-button v-else type="primary" @click="onScanBtnClick">开始扫描</el-button>
                        </div>
                    </div>
                </div>
                <el-dialog title="从控配置" :visible.sync="networkNodeConfigDialogVisible" width="25%" @closed="onNetworkNodeConfigDialogClose">
                    <el-form :model="networkNodeConfigForm" label-width="120px" label-position="left" size="medium" class="schemeTimingForm" style="width: 90%; margin-top: 10px;">
                        <el-form-item label="MAC地址" prop="address">
                            <el-input v-model="networkNodeConfigForm.address" disabled></el-input>
                        </el-form-item>
                            
                        <el-form-item label="灯类型" prop="lightType">
                            
                            <el-select v-model="networkNodeConfigForm.lightType" placeholder="请选择灯类型">
                                <el-option v-for="item in lightTypeDict" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="灯号" prop="lightNumber">
                            <el-input-number v-model="networkNodeConfigForm.lightNumber" :min="0"></el-input-number>
                        </el-form-item> 
                        <el-form-item label="所属主控" prop="masterControllerId">
                            <el-select v-model="networkNodeConfigForm.masterControllerId" placeholder="请选择主控">
                                <el-option v-if="networkNodeConfigForm.masterControllerId == defaultMasterControllerId" 
                                    label="无" :value="defaultMasterControllerId"></el-option>
                                <el-option v-else-if="networkNodeConfigForm.masterControllerId != adHocControllrtInfo.code" 
                                    :label="networkNodeConfigForm.masterControllerName" :value="networkNodeConfigForm.masterControllerId"></el-option>
                                <el-option label="当前主控" :value="adHocControllrtInfo.code"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <span slot="footer">
                        <el-button type="primary" :loading="btnLoadingStatus.networkConfig" @click="configNetworkNode">下放</el-button>
                        <el-button @click="networkNodeConfigDialogVisible = false">取消</el-button>
                    </span>
                </el-dialog>
            </el-tab-pane>
            <el-tab-pane :label="tabPanelLabels['planTab']" name="planTab" style="padding: 0px;">
                <div v-for="(plan, i) in plans" :key="'plan_' + i" class="planDiv">
                    <span class="planDivNo">{{i+1}}</span>
                    <div class="planDivContent">
                        <el-select :value="plan.controlType" placeholder="重复方式" class="el-icon-date" @change="$set(plans[i], 'controlType', $event)">
                            <el-option-group
                                    v-for="(group, j) in dateOptionsForPlan"
                                    :key="'dateSelectOptionGroup_' + i + '_' + j"
                                    :label="group.label">
                                <el-option
                                        v-for="(item, k) in group.options"
                                        :key="'dateSelectOption_' + i + '_' + j + '_' + k"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-option-group>
                        </el-select>
                        <el-time-picker is-range :clearable="false" v-model="planTimeRange(i)" range-separator="至" start-placeholder="开始时间"
                                        end-placeholder="结束时间" placeholder="选择时间范围" style="margin: 0 10px;">
                        </el-time-picker>
                        <el-select :value="plan.schemeNo" placeholder="控制方案" class="el-icon-document" @change="$set(plans[i], 'schemeNo', $event)">
                            <el-option-group
                                    v-for="(group, j) in schemeSelectOptions"
                                    :key="'schemeSelectOptionGroup_' + i + '_' + j"
                                    :label="group.label">
                                <el-option
                                        v-for="(item, k) in group.options"
                                        :key="'schemeSelectOption_' + i + '_' + j + '_' + k"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-option-group>
                        </el-select>

                    </div>
                    <el-divider direction="vertical"></el-divider>
                    <div class="planDivOperationBtnGroup">
                        <i class="el-icon-edit"></i>
                        <i class="el-icon-open"></i>
                        <i class="el-icon-delete"></i>
                    </div>
                    <el-divider></el-divider>
                </div>
                <i class="el-icon-circle-plus-outline newPlanBtn" @click="onNewPlanBtnClick()"></i>
            </el-tab-pane>
            <el-tab-pane v-for="(scheme, i) in schemes" :key="'schemePane_' + scheme.schemeNo"
                         :name="'schemePane_' + scheme.schemeNo" :label="tabPanelLabels['schemePane_' + scheme.schemeNo]" class="schemePane">
                <div class="signalTimingDivsWrapper">
                    <el-dialog title="方案参数" :visible.sync="schemeDialogsVisible[i]" width="25%">
                        <el-form :model="schemeParamsFormData" label-width="100px" label-position="left" size="medium" class="schemeTimingForm">
                            <el-form-item label="周期总长" prop="cycleTotalLength">
                                <el-input v-model.number="schemeParamsFormData.cycleTotalLength">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="黄灯时长" prop="yellowLight">
                                <el-input v-model.number="schemeParamsFormData.yellowLight">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="全红时长" prop="wholeRed">
                                <el-input v-model.number="schemeParamsFormData.wholeRed">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="安全绿时" prop="safeGreen">
                                <el-input v-model.number="schemeParamsFormData.safeGreen">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="机动绿闪" prop="motoVehiGreenFlash">
                                <el-input v-model.number="schemeParamsFormData.motoVehiGreenFlash">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="最大绿长" prop="maxGreen">
                                <el-input v-model.number="schemeParamsFormData.maxGreen">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                        </el-form>
                        <span slot="footer">
                            <el-button type="primary" @click="switchSchemeDialogsVisible(i, false)">确 定</el-button>
                        </span>
                    </el-dialog>
                    <div class="signalTimingDiv" v-for="(light, j) in scheme.lightParam" :key="'timingSlider_' + i + '_' + j">
<%--                    <span :class="'signalTimingDivNo ' + (checkedSignals.indexOf(j + 1) < 0 ? '' : 'checkedSignalsTimingDivNo')"--%>
<%--                          @click="onSignalTimingDivNoChecked(j + 1)">--%>
<%--                        {{ checkedSignals.indexOf(j + 1) < 0 ? j + 1 : "✓"}}--%>
<%--                    </span>--%>
                        <div :class="'signalTimingDivHeader ' + (checkedSignals.others.indexOf(light.lightNumber) < 0 ? '' : 'checkedSignalsTimingDivHeader')"
                             @click="onSignalTimingDivNoChecked(light.lightNumber)">
                            <div>
                                <img v-if="checkedSignals.others.indexOf(light.lightNumber) < 0" :src="'${ctxStatic}/image/adHoc/simpleIcon/' + lightTypeDict.find(e => e.value == light.lightType).remarks + '.png'">
                                <i v-else class="el-icon-check"></i>
                            </div>
                            <div>{{light.lightNumber}}</div>
                        </div>
                        <el-divider direction="vertical"></el-divider>
                        <%-- :ref="'signalTimingSlider' + scheme.schemeNo + '_' + light.lightNumber"  --%>
                        <signal-timing-slider 
                            :green-start-point.sync="light.greenStartTime" 
                            :green-duration.number.sync="light.frequentGreen" :yellow-duration="light.lightType == pedestrianLightType ? 0 : scheme.yellowLight" 
                            :all-red-duration="scheme.wholeRed" :max-green="scheme.maxGreen"
                            :cycle-duration="scheme.cycleTotalLength" @slidden="onSignalTimingSlidden(i, light.lightNumber, $event)"
                            @mousedown.native="checkedSignals.target = light.lightNumber;">
                        </signal-timing-slider>
                        <el-divider direction="vertical"></el-divider>
                        <i :class="'el-icon-arrow-right collapseArrow ' + (checkedCollapsePanel == light.lightNumber ? 'collapseArrowTranslation' : '')"
                           @click="onCollapseArrowClick(light.lightNumber)"></i>
                        <el-collapse-transition>
                            <div v-show="checkedCollapsePanel == light.lightNumber" class="signalTimingFormWrapper">
                                <el-form :model="light" label-width="3rem" hide-required-asterisk class="signalTimingForm">
                                    <div v-for="(paramGroup, k) in lightParamTemplate" :key="'lightPara_' + i + '_' + j +  '_' + k" class="formItemRow">
                                        <el-form-item v-for="(p, l) in paramGroup" :key="'lightPara_' + i + '_' + j +  '_' + k +  '_' + l"  :label="p.label" prop="">
                                            <el-input v-model.number="light[p.valueName]" size="mini" @input="onSignalTimingFormInputChange(i, light.lightNumber, p.valueName, $event)">
                                                <template slot="append">秒</template>
                                            </el-input>
                                        </el-form-item>
                                    </div>
                                </el-form>
                            </div>
                        </el-collapse-transition>
                        <el-divider></el-divider>
                    </div>
                </div>
                <div class="schemesTimingDiv">
                
                    <el-button-group>
                        <el-button type="primary" @click="updateSchemeInfo(scheme.schemeNo)" v-if="${fns:getUser().admin}" class="invisibleElement">保存</el-button>
                        <el-button type="primary" @click="putSchemeInfo(scheme.schemeNo)">下放</el-button>
                        <el-button type="primary" @click="onSchemeStartBtnClick(scheme.schemeNo)">启动</el-button>
                        <el-button @click="getSchemeInfo(scheme.schemeNo)">重置</el-button>
                    </el-button-group>
                    <el-button type="primary" icon="el-icon-setting" @click="switchSchemeDialogsVisible(i, true)">方案参数</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane name="tabAddBtn" label="+"></el-tab-pane>
        </el-tabs>
        <i class="el-icon-s-tools gloablSettingBtn" @click="onGlobalSettingBtnClick"></i>
        <el-dialog title="全局设置" :visible.sync="globalSettingDialogVisible" width="25%">
            <el-form :model="adHocControllrtInfo" label-width="120px" label-position="left" size="medium" class="schemeTimingForm">
                <el-divider>通讯参数</el-divider>
                <el-form-item label="心跳包间隔" prop="heartBeatInterval">
                    <el-input v-model.number="adHocControllrtInfo.heartBeatInterval">
                        <template slot="append">秒</template>
                    </el-input> 
                </el-form-item>
                <el-form-item label="数据包间隔" prop="dataInterval">
                    <el-input v-model.number="adHocControllrtInfo.dataInterval">
                        <template slot="append">秒</template>
                    </el-input> 
                </el-form-item>
                <el-divider>调光参数</el-divider>
                <el-form-item label="开始时间" prop="dimmingStartTime">
                    <el-time-picker v-model="adHocControllrtInfo.dimmingStartTime" placeholder="开始时间" :clearable="false"></el-time-picker>
                </el-form-item>
                <el-form-item label="结束时间" prop="dimmingEndTime">
                    <el-time-picker v-model="adHocControllrtInfo.dimmingEndTime" placeholder="结束时间" :clearable="false"></el-time-picker>
                </el-form-item>
                <el-form-item label="调光控制" prop="dimmingControlStatus">
                    <div class="flexAlignCenterWrapper">
                        <el-switch v-model="adHocControllrtInfo.dimmingControlStatus"></el-switch>
                    </div>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="onGlobalSettingPutBtnClick">下放</el-button>
                <el-button @click="onGlobalSettingResetBtnClick">重置</el-button>
            </span>
        </el-dialog>

    </div>
</div>

<script type="module">
    let signalTimingSlider = await assembleVueComponent('signalTimingSlider/signalTimingSlider.html', signalTimingSliderScript);
    let sketchMap = await assembleVueComponent('sketchMap/sketchMap.html', sketchMapScript);
    $(() => new Vue({
        el: '#adHocDetailConfig',
        components: { signalTimingSlider, sketchMap },
        computed: {
            sketchMapImage() {
                let regex = /\/[^|]+\.(?:png|jpg|jpeg|gif|bmp)/gm
                return regex.exec(this.adHocControllrtInfo.pictureUrl)?.[0]
            },
            tabPanelLabels() {
                return {
                    netWorkTab: '通讯配置',
                    planTab: '方案配置',
                    ...this.schemes.reduce((acc, e) => {
                        acc['schemePane_' + e.schemeNo] = '方案0' + e.schemeNo
                        return acc
                    }, {})
                }
            },

            schemeSelectOptions() {
                let result = [{label: '自定义方案', options: []}, {label: '特殊方案', options: []}]
                for (const s of this.schemes)
                    result[0].options.push({value: s.schemeNo, label: '方案0' + s.schemeNo})
                for (const s of this.specialSchemes)
                    result[1].options.push({value: s.schemeNo, label: s.schemeName})
                return result
            },
            planTimeRange: {
                get() {
                    return i => [this.time2Date(this.plans[i].schemeStartTime), this.time2Date(this.plans[i].schemeEndTime)]
                },
                set() {

                        console.log(v)

                }
            }

        },
        mounted() {
            // this.adHocId = new URL(window.location.href).searchParams.get('id');
            this.adHocId = new URLSearchParams(window.location.search).get('id');
            this.getAdHocControllerInfo();
            this.getSchemeInfo();
            this.getLightTypeDict();
        },
        data() {
            return {
                adHocId: '',
                unknownAdHocControllrtInfo: {name: '未知设备'},
                adHocControllrtInfo: {},
                otherAdHocControllrtInfos: {},
                lightInfo: {},
                pedestrianLightType: '3',

                activeTab: 'netWorkTab',
                scanStatus: 0,
                scanInterval: null,
                scanResultTableData: [],
                networkNodeConfigForm: {},
                preSetNetworkNodeConfigForm: {
                    address: '0000000000000000',
                    lightType: '0',
                    lightNumber: '0',
                    masterControllerId: '0000000000000000'
                },

                schemes: [],
                schemeParamsFormData: {
                    cycleTotalLength: 120,
                    yellowLight: 3,
                    wholeRed: 3,
                    safeGreen: 5,
                    motoVehiGreenFlash: 5,
                    maxGreen: 30
                },
                specialSchemes: [
                    {
                    schemeNo: 99,
                    schemeName: '灭灯'
                },{
                    schemeNo: 100,
                    schemeName: '黄闪'
                }],
                presetsSchemes: {
                    id: '',
                    schemeNo: 0,
                    motoVehiGreenFlash: 3,
                    safeGreen: 15,
                    yellowLight: 3,
                    wholeRed: 3,
                    maxGreen: 50,
                    cycleTotalLength: 200
                },
                plans: [
                    {
                    planNo: 1,
                    controlType: 1,
                    schemeNo: 99,
                    schemeStartTime: '00:34:21',
                    schemeEndTime: '00:34:21',
                    enabled: 1
                }, {
                    planNo: 2,
                    controlType: 1,
                    schemeNo: 2,
                    schemeStartTime: '00:34:21',
                    schemeEndTime: '00:34:21',
                    enabled: 1
                }, {
                    planNo: 3,
                    controlType: 1,
                    schemeNo:3,
                    schemeStartTime: '00:34:21',
                    schemeEndTime: '00:34:21',
                    enabled: 1
                }],

                defaultMasterControllerId: '00000000000',
                dateOptionsForPlan: [{
                    label: '单日重复',
                    options: [{label: '周一', value: 1},{label: '周二', value: 2},{label: '周三', value: 3},
                        {label: '周四', value: 4},{label: '周五', value: 5},{label: '周六', value: 6},{label: '周日', value: 7}]
                }, {
                    label: '多日组合',
                    options: [{label: '工作日', value: 8},{label: '休息日', value: 9},{label: '整周', value: 10}]
                }],
                
                checkedSignals: {target: 0, others: []},
                checkedCollapsePanel: 0,
                
                btnLoadingStatus: {
                    networkConfig: false,
                    networkComplete: false,
                    stopScan: false 
                },

                schemeDialogsVisible: [false, false, false, false],
                networkNodeConfigDialogVisible: false,
                globalSettingDialogVisible: false,
                
                lightTypeDict: [],
                lightParamTemplate: [[
                    {label: '起点', valueName: 'greenStartTime'},
                    {label: '绿长', valueName: 'frequentGreen'}
                ], [
                    {label: '红计时', valueName: 'redTime'},
                    {label: '绿计时', valueName: 'greenTime'},
                ]],

                displaiedSignals: []
            }
        },
        methods: {
            async getAdHocControllerInfo() {
                try {
                    let {data: collectorInfo} = await jsonFetch("${path}/adHoc/a/signal/tblCollector/get?id=" + this.adHocId, "post", null, "无法获取信号机信息")
                    let {data: alarmConfig} = await jsonFetch("${path}/adHoc/a/signal/tblAlarmConfig/getAlarmConfig?collectorId=" + this.adHocId, "post", null, "无法获取通讯配置")
                    collectorInfo.heartBeatInterval = alarmConfig.stateInterval
                    collectorInfo.dataInterval = alarmConfig.dataInterval
                    this.adHocControllrtInfo = collectorInfo
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async getSchemeInfo(schemeNo='') {
                try {
                    let {data: result} = await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/getSchemeInfo?id=" + this.adHocId + "&schemeNo=" + schemeNo, "post", null, "无法获取方案信息")
                    let schemeIndex = this.schemes.findIndex(e => e.schemeNo == schemeNo)

                    this.preProcessReceivedSchemeData(result??[])   

                    if(schemeNo) 
                        this.$set(this.schemes, schemeIndex == -1 ? this.schemes.length : schemeIndex, result[0])
                    else 
                        this.$set(this, 'schemes', result??[])
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async getLightTypeDict() {
                let {data: lightTypeDict} = await jsonFetch("${path}/adHoc/common/getDictByType?type=light_type", "post", null, "获取灯类型字典失败")
                this.lightTypeDict = lightTypeDict
            },
            async addNewScheme() {  
                if(this.schemes.length >= 4) 
                    return ELEMENT.Message.warning('方案数量已达上限，无法继续添加。')
                
                try {
                    let schemeNo = this.schemes.length + 1
                    let result = await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/addSchemeInfo", "post",
                        this.preProcessPostSchemeData(schemeNo, 'add'), "无法添加新方案")
                    await this.getSchemeInfo(schemeNo)
                    this.activeTab = 'schemePane_' + schemeNo
                    ELEMENT.Message.success('方案添加成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async updateSchemeInfo(schemeNo) {
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/updateSchemeInfo", "post",
                        this.preProcessPostSchemeData(schemeNo, 'update'), "无法更新方案信息")
                    await this.getSchemeInfo(schemeNo)
                    ELEMENT.Message.success('方案更新成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async putSchemeInfo(schemeNo) {
                try {
                    await this.updateSchemeInfo(schemeNo)
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/putSchemeInfo", "post",
                        this.preProcessPostSchemeData(schemeNo, 'put'), "方案下放失败")
                    ELEMENT.Message.success('方案下放成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }   
            },
            async onSchemeStartBtnClick(schemeNo) {
                try {
                    ELEMENT.MessageBox.confirm('是否启动方案?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                        beforeClose: async (action, instance, done) => {
                            if(action == 'confirm') {
                                try {
                                    instance.confirmButtonLoading = true
                                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/putPortableSchemeLevelInfo", "post",
                                        {id: this.adHocId, schemeNo: schemeNo}, "方案启动失败")
                                    ELEMENT.Message.success('方案已启动')
                                } catch (e) {
                                    ELEMENT.Message.error(e.message)
                                } finally {
                                    instance.confirmButtonLoading = false
                                }
                            }
                            done()
                        }
                    })
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            onGlobalSettingBtnClick() {
                this.globalSettingDialogVisible = true
            },
            async onTabClick(e) {
                switch (e.name) {
                    case "netWorkTab":
                        break;
                    case "planTab":
                        console.log(await jsonFetch("${path}/adHoc/a/signal/adHOCTimePlan/getTimePlan?id=" + this.adHocId, "post", null, ""))
                        break;
                    case "tabAddBtn":
                        this.addNewScheme()
                        break;
                    default:
                        this.getSchemeInfo(e.name.split('_')[1])
                        break;
                }
            },
            async onTabRemoveBtnClick(paneName) {
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/deleteSchemeInfo?id=" + this.adHocId
                        + "&schemeNo=" + paneName.split('_')[1], "post", null, "方案删除失败")
                    await this.getSchemeInfo()
                    
                    this.activeTab = 'schemePane_1'
                    ELEMENT.Message.success('方案删除成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async onScanBtnClick() {
                this.scanStatus++;
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putStartNetworkInfo?id=" + this.adHocId, "post", null, "无法开始组网")
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putAdHOCQueryInfo", "post", {id: this.adHocId, adHOCQType: 1}, "无法开始扫描")
                    let requestComplete = true
                    this.scanInterval = window.setInterval(async () => {
                        if(!requestComplete) return
                        requestComplete = false
                        requestComplete = await this.getScanResult()
                    }, 3000)
                } catch (e) {
                    this.scanStatus = 0
                    ELEMENT.Message.error(e.message)
                }
            },
            async getScanResult() {
                try {
                    let {data: scanResult = []} = await jsonFetch("${path}/adHoc/a/signal/adHOCConfig/getLightInfoTemp?id=" + this.adHocId + "&lightNumber=", "post", null, "无法获取扫描信息")
                    let {data: lightInfo = []} = await jsonFetch("${path}/adHoc/a/signal/adHOCConfig/getLight?id=" + this.adHocId, "post", null, "获取灯信息失败")
                    
                    await Promise.all(scanResult.map(async e => {
                        let light = lightInfo.find(ee => ee.address == e.address)
                        if(!light && e.masterControllerId == this.adHocControllrtInfo.code && e.lightNumber != '0') {
                            await jsonFetch("${path}/adHoc/a/signal/adHOCConfig/addLight", "post", {
                                collectorId: this.adHocId,
                                enabled: 1,
                                address: e.address, 
                                lightNumber: e.lightNumber, 
                                lightType: e.lightType,
                                signalLightDeliveryTime: new Date(),
                                signalLightEndTime: new Date(new Date().getTime() + 3 * 365 * 24 * 60 * 60 * 1000)
                            }, "无法添加从控记录")
                        } else if(light && e.masterControllerId != this.adHocControllrtInfo.code) {
                            await jsonFetch("${path}/adHoc/a/signal/adHOCConfig/deleteLight?id=" + this.adHocId +
                                "&address=" + e.address, "post", null, "无法删除从控记录")
                        }

                        if(e.masterControllerId != this.adHocControllrtInfo.code && e.masterControllerId != this.defaultMasterControllerId) {
                            let masterInfo = this.otherAdHocControllrtInfos[e.masterControllerId]
                            if(!masterInfo) {
                                try {
                                    ({data: masterInfo} = await jsonFetch("${path}/adHoc/a/signal/tblCollector/getByCode?code=" + e.masterControllerId, "post", null, "无法获取主控信息[" + e.masterControllerId + "]"))
                                    this.otherAdHocControllrtInfos[e.masterControllerId] = masterInfo
                                } catch (e) {
                                    console.error(e.message)
                                }
                            } 
                            e.masterControllerName = (masterInfo??this.unknownAdHocControllrtInfo).name
                        }
                    }))

                    this.scanResultTableData = scanResult??[]
                    this.scanStatus++
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
                return true
            },
            onNetworkNodeNetworkStatusLinkClick(lightInfo) {
                if(lightInfo.masterControllerId != this.adHocControllrtInfo.code)
                    return ELEMENT.Message.error('该设备不属于当前网络，无法移出网络')
                
                ELEMENT.MessageBox.confirm('是否将该设备移出当前网络?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    beforeClose: async (action, instance, done) => {
                        try {
                            if(action == 'confirm') {
                                instance.confirmButtonLoading = true
                                await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putDeleteAdHOCLightInfo?id=" +
                                    this.adHocId +"&followLightAdress=" + lightInfo.address + "&masterLightId=" +
                                    lightInfo.masterControllerId, "post", null, "无法将该设备移出网络")
                                instance.confirmButtonLoading = false
                                ELEMENT.Message.success('已将该设备移出网络')
                            } 
                        } catch (e) {
                            instance.confirmButtonLoading = false
                            ELEMENT.Message.error(e.message)
                        }
                        done()
                    }
                })
            },
            async onNetworkNodeSearchLightBtnClick(lightInfo) {this.$set(lightInfo, 'isActive', !lightInfo.isActive)
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putAdHOCFollowControllerInfo?id=" + this.adHocId +
                        "&followLightAdress=" + lightInfo.address + "&status=" + +!lightInfo.isActive, "post", null, "获取灯信息失败")
                    this.$set(lightInfo, 'isActive', !lightInfo.isActive)
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            onNetworkNodeLocationBtnClick(lightNumber) {
                this.$refs['sketchMap'].highlightElements(window.top.document, document.getElementsByClassName('componentLibraryWrapper'))
            },
            onNetworkNodeConfigBtnClick(lightInfo) {
                this.networkNodeConfigForm.address = lightInfo.address
                this.networkNodeConfigForm.lightType = lightInfo.lightType
                this.networkNodeConfigForm.lightNumber = lightInfo.lightNumber
                this.networkNodeConfigForm.masterControllerId = lightInfo.masterControllerId
                this.networkNodeConfigDialogVisible = true
            },
            async onNetworkCompleteBtnClick() {
                this.btnLoadingStatus.networkComplete = true
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putEndNetworkInfo?id=" + this.adHocId, "post", null, "无法发送结束组网指令")
                    window.clearInterval(this.scanInterval)
                    this.scanStatus = 0
                    this.btnLoadingStatus.networkComplete = false
                    ELEMENT.Message.success('组网完毕')
                } catch (e) {
                    this.btnLoadingStatus.networkComplete = false
                    ELEMENT.Message.error(e.message)
                }
            },
            async onStopScanBtnClick() {
                this.btnLoadingStatus.stopScan = true
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putEndScannerInfo?id=" + this.adHocId, "post", null, "无法发送停止扫描指令")
                    window.clearInterval(this.scanInterval)
                    this.btnLoadingStatus.stopScan = false
                    ELEMENT.Message.success('扫描已停止')
                } catch (e) {
                    this.btnLoadingStatus.stopScan = false
                    ELEMENT.Message.error(e.message)
                }
            },
            onNetworkNodeConfigDialogClose() {
                this.networkNodeConfigForm = JSON.parse(JSON.stringify(this.preSetNetworkNodeConfigForm))
            },
            async configNetworkNode() {
                this.btnLoadingStatus.networkConfig = true
                try {
                    if(this.networkNodeConfigForm.lightNumber == '0') 
                        throw new Error('从控灯号不能为0')
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putMacInfo", "post", {
                        id: this.adHocId,
                        lightNumber: this.networkNodeConfigForm.lightNumber,
                        lightType: this.networkNodeConfigForm.lightType,
                        followLightAdress: this.networkNodeConfigForm.address,
                        masterLightId: this.networkNodeConfigForm.masterControllerId,
                    }, "从控信息配置失败")
                    ELEMENT.Message.success('从控信息配置成功')
                    this.btnLoadingStatus.networkConfig = false
                    this.networkNodeConfigDialogVisible = false
                } catch (error) {
                    this.btnLoadingStatus.networkConfig = false
                    ELEMENT.Message.error(error.message)
                }
            },
            async onNewPlanBtnClick() {
                let newPlan = { id: this.adHocId, planNo: this.plans.length, controlType: 1, schemeNo: 1,
                    schemeStartTime: '00:00:00', schemeEndTime: '00:00:00', enabled: 1 }
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCTimePlan/addTimePlan", "post", newPlan, "新计划添加失败")
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
                this.plans.push(newPlan)
            },

            async onPlanDeleteBtnClick() {
                this.plans.push({
                    planNo: this.plans.length,
                    controlType: 1,
                    schemeNo: 1,
                    schemeStartTime: '00:00:00',
                    schemeEndTime: '00:00:00',
                    enabled: 1
                })
            },
            
            onSignalTimingDivNoChecked(signalNo) {
                let index = this.checkedSignals.others.indexOf(signalNo)
                if(index < 0)
                    this.checkedSignals.others.push(signalNo)
                else
                    this.checkedSignals.others.splice(index, 1)
            },
            onSignalTimingSlidden(schemeNo, lightNo, greenParams) {
                if(!this.checkedSignals.others.includes(lightNo)) return
                for (const light of this.schemes[schemeNo].lightParam.filter(e => this.checkedSignals.others.includes(e.lightNumber) && e.lightNumber != this.checkedSignals.target)) {
                    light.greenStartTime = greenParams[0]
                    light.frequentGreen = greenParams[1] - greenParams[0]
                }
                // console.log(JSON.stringify(this.schemes[schemeNo].lightParam[0]) )
            },
            onCollapseArrowClick(signalNo) {
                this.checkedCollapsePanel = this.checkedCollapsePanel == signalNo ? 0 : signalNo
            },
            onSignalTimingFormInputChange(schemeNo, lightNo, paramName, value) {
                for (const light of this.schemes[schemeNo].lightParam.filter(e => this.checkedSignals.others.includes(e.lightNumber) && e.lightNumber != lightNo)) {
                    light[paramName] = +value
                }
            },

            switchSchemeDialogsVisible(index, isShown) {
                if(isShown) {
                    this.schemeParamsFormData = JSON.parse(JSON.stringify(this.schemes[index]))
                } else {
                    if(this.schemeParamsFormData.yellowLight + this.schemeParamsFormData.wholeRed <= this.schemeParamsFormData.cycleTotalLength)
                        return ELEMENT.Message.warning('黄灯时长与全红时长之和应大于周期总长')
                    Object.assign(this.schemes[index], this.schemeParamsFormData)
                    for (const e of this.schemes[schemeNo].lightParam) {
                        if(e.greenStartTime + e.greenDuration + this.schemeParamsFormData.yellowLight + this.schemeParamsFormData.wholeRed > this.schemeParamsFormData.cycleTotalLength)
                            e.greenStartTime = this.schemeParamsFormData.cycleTotalLength - e.greenDuration - this.schemeParamsFormData.yellowLight - this.schemeParamsFormData.wholeRed
                        if(e.greenStartTime < 0) {
                            e.greenStartTime = 0
                            e.greenDuration =  this.schemeParamsFormData.cycleTotalLength - this.schemeParamsFormData.yellowLight - this.schemeParamsFormData.wholeRed
                        }
                    }
                }
                this.$set(this.schemeDialogsVisible, index, isShown)
            },

            preProcessPostSchemeData(schemeNo, requestType) {
                let result 

                if(requestType == 'add') {
                    result = JSON.parse(JSON.stringify(this.presetsSchemes))
                    result.schemeNo = schemeNo
                } else {
                    result = JSON.parse(JSON.stringify(this.schemes.find(e => e.schemeNo == schemeNo)))
                    this.renameObjectKey(result, 'lightParam', 'lightValueBeans')
                    if(requestType == 'put') delete result.maxGreen
                }
                delete result.collectorId
                result.id = this.adHocId
                return result
            },

            preProcessReceivedSchemeData(schemes){
                for(const scheme of schemes) {
                    this.renameObjectKey(scheme, 'alterableLightValueBeans', 'lightParam')
                    scheme.lightParam.sort((a, b) => a.lightNumber - b.lightNumber)
                }
                schemes = convertString2Numbers(schemes)
                schemes.sort((a, b) => a.schemeNo - b.schemeNo)
            },

            async onGlobalSettingPutBtnClick() {
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putAdHOCHeartBeat", "post", {
                        id: this.adHocId,
                        heartBeatInterval: this.adHocControllrtInfo.heartBeatInterval,
                        dataInterval: this.adHocControllrtInfo.dataInterval,
                        statusPartInvalidInterval: 0
                    }, "通讯配置下放失败")
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/putDimming", "post", {
                        id: this.adHocId,
                        dimmingParam: {
                            startTime: new Date(this.adHocControllrtInfo.dimmingStartTime).toLocaleTimeString(),
                            endTime: new Date(this.adHocControllrtInfo.dimmingEndTime).toLocaleTimeString(),
                            controlStatus: +this.adHocControllrtInfo.dimmingControlStatus 
                        }
                    }, "调光配置下放失败")
                    ELEMENT.Message.success('全局配置下放成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },

            onGlobalSettingResetBtnClick() {
                this.getAdHocControllerInfo()
            },

            getNetworkStatus(networkNode, withDetail = false) {
                return networkNode.masterControllerId == this.defaultMasterControllerId ? {text: '尚未组网', type: 'default'} :
                    (networkNode.masterControllerId == this.adHocControllrtInfo.code ? {text: '归属本机', type: 'success'} :
                        {text: '归属外控'+ (withDetail && networkNode.masterControllerName ? ('[' + networkNode.masterControllerName + ']') : ''), type: 'danger'})
            },

            renameObjectKey(obj, oldKey, newKey) {
                obj[newKey] = obj[oldKey]
                delete obj[oldKey]
            },
            formatDate(dateString) {
                let timestamp = new Date(dateString).getTime()
                return timestamp > new Date().setDate(new Date().getDate() - 1) ? new Date(timestamp).toLocaleTimeString() : '>24小时'
            },
            time2Date(time) {
                let temp = time.split(':')
                return new Date(0, 0, 0, temp[0], temp[1], temp[2])
            },
            dateArray2TimeStringArray(dateArray) {
                let timeStringArray = []
                for (const e of dateArray)
                    timeStringArray.push(e.toLocaleTimeString())
                return timeStringArray
            },
            isInInterval(start, end, value) {
                return value >= start && value <= end
            },
            goBack() {
                window.history.back()
            }
        },
        watch: {
            //activeTab(newVal) {
            //    this.$nextTick(() => {
            //        let schemeNo = newVal.split('_')[1]
            //        if(newVal.includes('schemePane_')) {
            //            for(let light of this.schemes.find(e => e.schemeNo == schemeNo).lightParam) {
            //                this.$refs['signalTimingSlider' + schemeNo + '_' + light.lightNumber][0].getSignalTimingSliderPosition()
            //            }
            //        }
            //    })
            //}
        }
    }))

</script>

<style>
    html {
        overflow: hidden !important;
    }

    .flexWrapper {
        display: flex;
        flex-direction: column;
        height: 100vh;
    }

    .adHocDetailConfigWrapper {
        display: flex;
        flex: 1;
        height: 0;
    }

    .panelBreadcrumb {
        font-size: 15px;
        padding: 5px;
        font-style: italic;
    }

    .sketchMapDiv {
        display: flex;
        flex-direction: column;
        flex: 1;
        padding: 5px;
    }

    .libraryComponentWrapper, .markedComponentWrapper {
        background-color: #F00;
    }

    .configTabs {
        width: 50%;
        flex: 1;
    }

    .configTabs .el-tabs__header.is-right {
        margin-left: 0;
    }

    .configTabs .el-tabs__nav-wrap.is-scrollable.is-right {
        padding: 0;
    }

    .configTabs .el-tabs__nav-prev, .configTabs .el-tabs__nav-next {
        display: none;
    }

    .configTabs .el-tabs__item.is-right{
        width: 1rem;
        height: auto;
        padding: 5px 20px;
        white-space: break-spaces;
        line-height: 1.2rem;
        border-bottom: 2px solid #dfdfdf !important;
        display: flex;
        flex-direction: column;
        align-items: center;
        user-select: none;
    }

    .configTabs .el-tabs__item.is-right .el-icon-close {
        margin: 0px;
    }

    .configTabs .el-tabs__item .el-icon-close {
        display: none;
    }

    .configTabs .el-tabs__item:first-child .el-icon-close, .configTabs .el-tabs__item:nth-child(2) .el-icon-close, .configTabs .el-tabs__item:last-child .el-icon-close {
        display: none !important;
    }

    .configTabs .el-tabs__item.is-active .el-icon-close {
        display: inline-block;
    }

    .configTabs .el-tabs__content {
        height: 100%;
        padding: 0;
    }

    .configTabs .el-tab-pane {
        overflow: auto;
        height: calc(100% - 10px);
        padding-top: 10px;
        scrollbar-width: thin;
    }

    .configTabs .el-tab-pane::-webkit-scrollbar {
        width: 5px;
    }

    .configTabs .el-tab-pane::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 5px;
    }

    .scanPanel {
        width: 100%;
        height: 100%;
    }

    .scanStartBtn {
        border: lightgrey 1px solid;
        border-radius: 100px;
        width: 10rem;
        height: 10rem;
        font-size: 23px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background-image: linear-gradient(#409effD0 , #409EFF);
        color: white;
        cursor: pointer;
        user-select: none;
    }

    .scanningBtn::before {
        content: "";
        width: 100%;
        height: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        background-image: linear-gradient(225deg, #FFFFFF00 50%, #FFFFFF 100%);
        transform-origin: left top;
        animation: scanRotate 2s infinite linear;
    }

    @keyframes scanRotate {
        from { transform: rotate(-180deg); }
        to { transform: rotate(180deg); }
    }

    .scanPanel .scanResultTableWrapper, .scanPanel .el-table, .schemePane {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .scanPanel .el-table th .cell {
        margin-left: -10px;
    }

    .scanPanel .el-table__body-wrapper {
        flex: 1;
        scrollbar-width: thin;
    }

    .scanPanel .el-table__body-wrapper::-webkit-scrollbar {
        height: 8px;
    }

    .scanPanel .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 5px;
    }

    .scanPanel .el-table .caret-wrapper {
        width: 0px;
    }

    .scanPanel .el-table .cell {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .scanPanel .el-table .cell {
        margin-left: -10px;
    }

    .scanPanel .el-link {
        font-size: medium;
    }

    .scanTableBtnGroup {
        display: flex;
        justify-content: space-evenly;
        font-size: 25px;
        color: #909399;
        user-select: none;
    }

    .scanTableBtnGroup>i {
        cursor: pointer;
    }

    .scanTableBtnGroup>i.isActive {
        color: #E6A23C !important;
    }

    .scanTableBtnGroup>i:hover {
        color: #409EFF;
    }

    .signalTimingDivsWrapper {
        flex: 1;
        overflow: auto;
    }

    .signalTimingDiv {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 0 5px 0 10px;
    }

    .signalTimingDivHeader, .signalTimingDivHeader div {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .signalTimingDivHeader {
        margin: 0 5px;
    }

    .signalTimingDivHeader div {
        border: 2px solid #606266;
        width: 1.5rem;
        height: 1.5rem;
        font-size: 18px;
        font-weight: bolder;
        color: #606266;
        cursor: pointer;
        user-select: none;
        -webkit-user-drag: none;
    }

    .signalTimingDivHeader div:first-child {
        background-color: #606266;
        color: white;
        font-weight: bolder !important;
        border-radius: 10px 0 0 10px;
        padding-left: 2px;
    }

    .signalTimingDivHeader div:nth-child(2) {
        border-radius: 0 10px 10px 0;
    }

    .checkedSignalsTimingDivHeader div {
        color: #409EFF;
        border-color: #409EFF;
    }

    .checkedSignalsTimingDivHeader div:first-child {
        background-color: #409EFF;
        padding: 0 1px;
    }

    .signalTimingDiv .el-divider.el-divider--vertical {
        height: 90px ;
    }

    .signalTimingSlider {
        flex: 1;
        padding: 0 5px;
    }

    .collapseArrow {
        font-size: 18px;
        margin: 0 5px;
        cursor: pointer;
        transform: rotateZ(0deg);
        transition: transform 0.2s;
    }

    .collapseArrowTranslation {
        font-size: 18px;
        margin: 0 5px;
        cursor: pointer;
        transform: rotateZ(90deg);
        transition: transform 0.2s;
    }

    .signalTimingFormWrapper {
        width: 100%;
    }

    .signalTimingForm {
        margin: 0;
    }

    .signalTimingForm .formItemRow {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
    }

    .signalTimingForm .el-form-item {
        display: flex;
        align-items: center;
        margin-bottom: 0px;
    }

    .signalTimingForm .el-form-item__label {
        box-sizing: content-box;
        text-align-last: justify;
    }

    .signalTimingForm .el-form-item__content {
        margin-left: 5px !important;
    }

    .signalTimingForm .el-input {
        width: 150px;
    }

    .signalTimingForm input[type="text"], .schemeTimingForm input[type="text"], .planDiv input[type="text"] {
        margin: 0px;
        text-align: center;
    }

    .signalTimingForm .el-input-group__append {
        padding: 0 10px;
    }

    .signalTimingDiv .el-divider--horizontal, .planDiv .el-divider--horizontal {
        margin: 12px 0;
    }

    .signalTimingDiv:last-child .el-divider--horizontal, .planDiv:last-child .el-divider--horizontal {
        margin: 12px 0 0 0;
        height: 0;
    }

    .schemesTimingDiv {
        background-color: #FFFFFF;
        display: flex;
        flex-direction: row-reverse;
        border-top: #dfdfdf 1px solid;
        padding: 10px;
        z-index: 1;
    }

    .schemeTimingForm {
        width: 90%;
        margin: auto;
    }

    .schemeTimingForm .el-form-item {
        height: 36px;
    }

    .schemeTimingForm .el-form-item__label{
        font-size: 16px;
    }

    .schemeTimingForm .el-input.is-disabled input{
        border-color: #CCC;
        color: #888;
    }

    .schemeTimingForm .el-select, .schemeTimingForm .el-input-number {
        width: 100%
    }

    .schemesTimingDiv .el-button {
        margin: 0 10px 0 0;
    }

    .planDiv {
        display: inline-flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-evenly;
        width: 100%;
        min-width: 720px;
    }

    .planDivNo {
        display: inline-flex;
        align-items: center;
        justify-content: space-evenly;
        margin: 0 10px;
        border: 2px solid #606266;
        border-radius:1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        font-size: 18px;
        font-weight: bolder;
        color: #606266;
        cursor: pointer;
        user-select: none;
    }

    .planDivContent {
        display: inline-flex;
        align-items: center;
        justify-content: space-evenly;
    }

    .planDivContent .el-select .el-input__inner{
        height: 40px;
    }

    .planDivContent .el-select {
        display: inline-flex;
        align-items: center;
        width: 150px;
    }

    .planDivContent .el-select::before {
        position: absolute;
        left: 10px;
        z-index:1;
        color:#5c5f64;
        font-size: 16px;
    }

    .planDivContent .el-select .el-input__inner {
        padding-left: 30px;
    }

    .planDivContent .el-select__caret, .planDivContent .el-range__icon{
        color: #5c5f64 !important;
    }

    .planDivContent .el-date-editor {
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        width: 230px;
        margin: 0 10px;
    }

    .planDivContent .el-date-editor .el-range__close-icon {
        display: none;
        width: 0px;
    }

    .planDiv .el-divider--vertical{
       height: 40px;
    }

    .planDivOperationBtnGroup i {
        font-size: 23px;
        color: #909399;
        margin-right: 5px;
        cursor: pointer;
    }

    .planDivOperationBtnGroup i:last-child {
        margin-right: 0px;
    }
    .newPlanBtn {
        display: block;
        text-align: center;
        font-size:30px;
        color: rgb(128 133 139);
        cursor: pointer;
    }

    .gloablSettingBtn {
        position: fixed;
        bottom: 3px;
        right: 3px;
        z-index: 8;
        font-size: 26px;
        border: 2px solid #909399;
        border-radius: 30px;
        padding: 2px;
        color: #909399;
        margin-bottom: 5px;
        cursor: pointer;
        opacity: 0.65;
    }

    #adHocDetailConfig .el-button {
        padding: 8px 12px;
    }
    #adHocDetailConfig .el-dialog {
        min-width: 400px;
    }
    #adHocDetailConfig .el-dialog__body {
        padding: 5px 20px;
    }
    #adHocDetailConfig .el-dialog__footer {
        padding-top: 0px;
    }

    .el-input-number__decrease {
        height: 34px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .el-input-number__increase {
        height: 34px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .el-input-number >.el-input >.el-input__inner {
        height: 36px !important;
    }

    .flexAlignCenterWrapper {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .rtlWrapper {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        direction: rtl;
    }
    .invisibleElement {
        display: none;
    }
    

    [v-cloak] {
        display: none;
    }
</style>
