package com.lumlux.signal.daemon.mina;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Properties;
import java.util.concurrent.LinkedBlockingQueue;

import com.lumlux.signal.daemon.parser.heinqi.PutPortableTrafficMacInfoHandler;
import org.apache.log4j.Logger;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;

import com.aliyun.datahub.DatahubClient;
import com.aliyun.datahub.common.data.RecordSchema;
import com.aliyun.datahub.model.ListShardResult;
import com.lumlux.signal.daemon.bean.TblCircuitHistory;
import com.lumlux.signal.daemon.bean.TblCollector;
import com.lumlux.signal.daemon.parser.AbstractHandler;
import com.lumlux.signal.daemon.parser.heinqi.*;
import com.lumlux.signal.daemon.parser.hongdian.Constants;
import com.lumlux.signal.daemon.parser.hongdian.HongdianUtil;
import com.lumlux.signal.daemon.rowmapper.TblCollectorRowMapper;
import com.lumlux.signal.daemon.util.ByteUtil;
import com.lumlux.signal.daemon.util.DataHubUtil;
import com.lumlux.signal.daemon.util.DataSourceUtil;
import com.lumlux.signal.daemon.util.ExecuteDbOperate;
import com.lumlux.signal.daemon.util.PreparedParamCallBack;

public class ThreadPoolTask implements Runnable, Serializable
{
	private transient Logger log = Logger.getLogger(ThreadPoolTask.class);
	private static final long serialVersionUID = 0;
    /*private static int consumeTaskSleepTime = 2000;*/
    // 保存任务所需要的数据
    /*private Object threadPoolTaskData;*/
    
    private IoSession session;
    private Object message;
    private LinkedBlockingQueue<String> linkedBlockingQueue;
    private LinkedBlockingQueue<TblCircuitHistory> circuitHistoryQueue;
    private DatahubClient datahubClient;
    public ThreadPoolTask(IoSession session, Object message,LinkedBlockingQueue<String> linkedBlockingQueue,LinkedBlockingQueue<TblCircuitHistory> circuitHistoryQueue,DatahubClient datahubClient)
    {
        this.session = session;
        this.message = message;
        this.linkedBlockingQueue = linkedBlockingQueue;
        this.circuitHistoryQueue = circuitHistoryQueue;
        this.datahubClient = datahubClient;
    }
    
    private DataHubUtil dataHubUtil = new DataHubUtil();
    private ListShardResult listShardResult;
    private String shardId;
    private RecordSchema schema;
    
    private static final String SQL_QUERY_COLLECTOR = "select * from tbl_collector where code= ? and del_flag= '0'";
    
    public void run()
    {
    	final String threadName = Thread.currentThread().getName();
    	//log.debug(threadName + " [ThreadPoolTask] Received Message : [sessionID-" + session.getId() + "] " +message.toString());

		IoBuffer messageIoBuffer = (IoBuffer) message;
		final byte[] receiveData = messageIoBuffer.array();
		if (receiveData != null && receiveData.length > 0) {
			log.debug(threadName + " [ThreadPoolTask] 收到数据包 : " + ByteUtil.asHex(receiveData));
			byte packetType;
			switch (receiveData[0]) {
				case Constants.HEAD:
					//宏电协议
					packetType = receiveData[1];
					AbstractHandler handler = null;
					
					// 管理离线
					final byte[] baImei = new byte[11];
					System.arraycopy(receiveData, 4, baImei, 0, 11);
					final String strBaImei = new String(baImei);
					
					//存入dataHub
					try
					{
						if(datahubClient!= null)
						{
							Properties p = new Properties();
							p.load(Thread.currentThread().getContextClassLoader().getResourceAsStream("signal.properties"));
							final String officeId = p.getProperty("officeId");
							final String projectName = p.getProperty("projectName");
							final String topicName  = p.getProperty("topicName");
							log.debug(threadName + " [ThreadPoolTask] officeId=" + officeId);
							log.debug(threadName + " [ThreadPoolTask] projectName=" + projectName);
							log.debug(threadName + " [ThreadPoolTask] topicName=" + topicName);
							DataSourceUtil.execute(new ExecuteDbOperate()
							{
								@Override
								public int execute(Connection connection) throws SQLException
								{
									log.debug(" [ThreadPoolTask] 查询对应的集中器 Execute SQL ===> " + SQL_QUERY_COLLECTOR);
									final TblCollector collector = DataSourceUtil.fillObject(SQL_QUERY_COLLECTOR, new TblCollectorRowMapper(), new PreparedParamCallBack()
									{
										@Override
										public int setParams(PreparedStatement ps) throws SQLException
										{
											ps.setString(1, strBaImei);
											return 0;
										}
									}, connection);
									log.debug(" [ThreadPoolTask] 查询对应的集中器 Parameter   ===> " + strBaImei);
									if(collector!=null)
									{
										if(officeId.indexOf(collector.getOfficeId())!=-1)
										{
											if(listShardResult==null || schema==null)
											{
												// 可通过listShard接口获取shard列表，所有ACTIVE的shard均可使用，暂时使用第一个
										        listShardResult = datahubClient.listShard(projectName, topicName);
										        // 获取第一个shard
										        shardId = listShardResult.getShards().get(0).getShardId();
										        // 创建schema
										        schema = datahubClient.getTopic(projectName, topicName).getRecordSchema();
											}
									        // 写入数据
									        String result = dataHubUtil.writeEntry(datahubClient, projectName, topicName, receiveData,schema,shardId);
									        if("1".equals(result))//返回值为1 需要重连
									        {
									        	// 可通过listShard接口获取shard列表，所有ACTIVE的shard均可使用，暂时使用第一个
										        listShardResult = datahubClient.listShard(projectName, topicName);
										        // 获取第一个shard
										        shardId = listShardResult.getShards().get(0).getShardId();
										        // 创建schema
										        schema = datahubClient.getTopic(projectName, topicName).getRecordSchema();
									        }
									        log.info(threadName + " [ThreadPoolTask] "+collector.getCode()+"存入datahub结果" + ("0".equals(result)?"成功":"失败"));
										}
									}
									return 0;
								}
							});
						}
					}
					catch (Exception e)
					{
						log.error(e.getMessage());
					}
					
					// 根据传输的数据来判断通信管道中是否存在标识为xx的通道
					if (SignalSessionIdManager.getSessionId(strBaImei) != null)
					{
						Long existSessionId = SignalSessionIdManager.getSessionId(strBaImei);
						log.debug(threadName + " [ThreadPoolTask] ExistSessionIdManager :" + SignalSessionIdManager.getSessionId(strBaImei));
						if (session.getId() != existSessionId)
						{
							// 释放之前的资源
							IoSession oldSession = session.getService().getManagedSessions().get(existSessionId);
							if(oldSession != null)
							{
								oldSession.removeAttribute(BusinessConstants.SESSION_KEY);
								oldSession.close(false);
							}
							session.setAttribute(BusinessConstants.SESSION_KEY, strBaImei);
							
							SignalSessionIdManager.join(strBaImei, session.getId());
						}
					}
					else
					{
						// 存入session
						session.setAttribute(BusinessConstants.SESSION_KEY, strBaImei);
						
						SignalSessionIdManager.join(strBaImei, session.getId());
						
						Calendar dtNow = Calendar.getInstance();
						int week = 0;
						switch (dtNow.get(Calendar.DAY_OF_WEEK))
						{
							case 1:
								week = 1;
								break;
							case 2:
								week = 2;
								break;
							case 3:
								week = 4;
								break;
							case 4:
								week = 8;
								break;
							case 5:
								week = 16;
								break;
							case 6:
								week = 32;
								break;
							case 7:
								week = 64;
								break;
							default:
								break;
						}
						byte[] innerContent = new byte[] { (byte) 0xaa, 0x01, 0x18, 0x00, 0x07, 
								(byte) (dtNow.get(Calendar.YEAR) - 2000),
								(byte) (dtNow.get(Calendar.MONTH) + 1), 
								(byte) dtNow.get(Calendar.DAY_OF_MONTH), 
								(byte) dtNow.get(Calendar.HOUR_OF_DAY), 
								(byte) dtNow.get(Calendar.MINUTE), 
								(byte) dtNow.get(Calendar.SECOND), 
								(byte) week, (byte) 0xaa };
						char[] c = strBaImei.toCharArray();
						byte[] b = new byte[c.length];
						for (int i = 0; i < c.length; i++) {
							b[i] = (byte)c[i];
						}
						byte[] sendData = HongdianUtil.generateHongdianSendContent(innerContent, b);
						log.info(strBaImei + " 开始发送校时命令");
						log.debug(strBaImei + " 开始发送校时命令 " + ByteUtil.asHex(sendData));
						session.write(IoBuffer.wrap(sendData));
					}
					log.debug(threadName + " [ThreadPoolTask] SessionIdManager :" + SignalSessionIdManager.getSessionId(strBaImei));
					
					// 检测心跳线程问题
					Thread threadOnline = ThreadWorkManager.getThreadOnline();
					if(threadOnline == null)
					{
						threadOnline = new Thread(new ThreadOnlineManager(linkedBlockingQueue));  
						threadOnline.start();
						ThreadWorkManager.setThreadOnline(threadOnline);

						threadOnline = new Thread(new ThreadColumnOnlineManager(linkedBlockingQueue));
						threadOnline.start();
						ThreadWorkManager.setThreadOnline(threadOnline);
					}
					else
					{
						log.info(threadName + " [ThreadPoolTask] ThreadOnline State:" + threadOnline.getState());
					}
					// End 检测心跳线程问题
					
					ThreadWorker threadWorker = ThreadWorkManager.getThreadWork(strBaImei);
					if(threadWorker == null)
					{
						threadWorker = new ThreadWorker(strBaImei,linkedBlockingQueue,circuitHistoryQueue);
						ThreadWorkManager.join(strBaImei, threadWorker);
						new Thread(threadWorker).start(); 
					}
					WorkInfo workInfo = new WorkInfo(receiveData, session, strBaImei, System.currentTimeMillis());
					threadWorker.addData(workInfo);
					log.info(threadName + " [ThreadPoolTask] 分配处理人  "+workInfo+ " 线程数据个数："+threadWorker.getQueueCount());
					break;
					/*web端发数据，数据先包裹一层恒奇协议，在signalHandle中判断去哪一个内部协议*/
				case com.lumlux.signal.daemon.parser.heinqi.Constants.HEAD:
					//内层协议
					packetType = receiveData[1];
					switch (packetType) {
						//获取数据（发送故障时主动上报）
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.GET_DATA_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web请求数据开始");
							handler = new GetDataHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web请求数据结束");
							break;
						//获取基准功率及上下限
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.GET_LIMIT_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web请求基准数据开始");
							handler = new GetLimitHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web请求基准数据结束");
							break;
						//下放基准功率及上下限
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_LIMIT_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放基准数据开始");
							handler = new PutLimitHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放基准数据结束");
							break;
						//配置时间
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_TIME_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放时间开始");
							handler = new PutTimeHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放时间结束");
							break;
						//配置板卡数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CARD_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放板卡开始");
							handler = new PutCardHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放板卡结束");
							break;
						//手动配置启用位
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_START_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放启用开始");
							handler = new PutStartHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放启用开始");
							break;
						//获取数据（发送故障时主动上报）
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.GET_NEW_DATA_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web请求数据开始");
							handler = new GetNewDataHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web请求数据结束");
							break;
						//下放控制
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CONTROL_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放控制开始");
							handler = new PutControlHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放控制结束");
							break;
						//获取版本号
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.GET_VERSION_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web获取版本开始");
							handler = new GetVersionHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web获取版本结束");
							break;
						//下放报警配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CONFIG_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放心跳配置开始");
							handler = new PutConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放心跳配置结束");
							break;
							//下放控制器调光配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CONFIG_REQUEST_DIMMING:
							log.info(threadName + " [ThreadPoolTask] web下放调光配置开始");
							handler = new PutConfigDimmingHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放调光配置结束");
							break;

                           //下放黄灯单元配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CONFIG_YELLOW_LIGTH:
							log.info(threadName + " [ThreadPoolTask] web下放黄灯单元开始");
							handler = new PutConfigYellowLigthHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放黄灯单元结束");
							break;


						//下放控制器配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CONFIG_REQUEST_STRAGY:
							log.info(threadName + " [ThreadPoolTask] web下放策略配置开始");
							handler = new PutVariableStragyHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放策略报警配置结束");
							break;
						//下放单个控制器策略测试配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CONFIG_REQUEST_STRAGY_TEST:
							log.info(threadName + " [ThreadPoolTask] web下放策略配置开始");
							handler = new PutVariableStragyTestHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放策略报警配置结束");
							break;

						//获取报警配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.GET_CONFIG_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web获取报警配置开始");
							handler = new GetConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web获取报警配置结束");
							break;
						//下放时控
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_TIME_CONTROL_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放时控开始");
							handler = new PutTimeControlHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放时控结束");
							break;
						//下放启用位
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_TIME_CONTROL_ENBALED_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放启用时控开始");
							handler = new PutTimeControlEnabledHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放启用时控结束");
							break;
						//下放通知
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.INFORM_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放通知开始");
							handler = new InformHandler(linkedBlockingQueue);
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放通知结束");
							break;
						//获取时间
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.GET_TIME_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web获取时间开始");
							handler = new GetTimeHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web获取时间结束");
							break;
						//下放可变车道配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_VARIABLE_CONFIG_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放可变车道配置开始");
							handler = new PutVariableConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放可变车道配置结束");
							break;
						//获取可变车道配置
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.GET_VARIABLE_CONFIG_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web获取可变车道配置开始");
							handler = new GetVariableConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web获取可变车道配置结束");
							break;
						//下放二代基准
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_LIMIT_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放二代基准开始");
							handler = new SecondPutLimitHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放二代基准结束");
							break;
						//下放二代参数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_PARAMETER_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放二代参数开始");
							handler = new SecondPutParameterHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放二代参数结束");
							break;
						//下放设备重启
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_DEViICE_RESTART:
							log.info(threadName + " [ThreadPoolTask] web下放设备重启");
							handler = new SecondPutDeviceRestartHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放设备重启参数结束");
							break;
						//下放二代参数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_COLUMNCONFIG_RESTART:
							log.info(threadName + " [ThreadPoolTask] web下放心跳设置协议");
							handler = new SecondPutColumnConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放心跳设置协议参数结束");
							break;
						//下放二代参数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_RED_RESTART:
							log.info(threadName + " [ThreadPoolTask] web下放红投影");
							handler = new SecondPutRedProjectorHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放红投影下放参数结束");
							break;
						//下放二代参数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_GREEN_RESTART:
							log.info(threadName + " [ThreadPoolTask] web下放绿投影");
							handler = new SecondPutGreenProjectorHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放绿投影参数结束");
							break;
						//下放二代参数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_HRON_RESTART:
							log.info(threadName + " [ThreadPoolTask] web下放音响");
							handler = new SecondPutHornHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放音响参数结束");
							break;
						//下放二代参数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_DISPLAY_RESTART:
							log.info(threadName + " [ThreadPoolTask] web下放显示屏");
							handler = new SecondPutDisplayHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放显示屏参数结束");
							break;
						//获取二代参数
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_GET_PARAMETER_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web获取二代参数开始");
							handler = new SecondGetParameterHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web获取二代参数结束");
							break;
						//获取二代参考值
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_GET_REFERENCE_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web获取二代参考值开始");
							handler = new SecondGetReferenceHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web获取二代参考值结束");
							break;
						//下放二代启用位
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_START_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放二代启用位开始");
							handler = new SecondPutStartHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放二代启用位结束");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.SECOND_PUT_RESET_LIMIT_REQUEST:
							log.info(threadName + " [ThreadPoolTask] web下放二代复位功率上下限开始");
							handler = new SecondPutResetLimitHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] web下放二代复位功率上下限结束");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CONFIG_CABINET:
							log.info(threadName + " [ThreadPoolTask] 机柜型故障检测器故障判决迭代次数开始");
							handler = new CabinetPutConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 机柜型故障检测器故障判决迭代次数开始");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_CAMERA_COME_PERSON:
							log.info(threadName + " [ThreadPoolTask] 摄像头来人开始");
							handler = new CameraComePersonHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 摄像头来人结束");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ADHOC_SCHEME_CONFIG:
							log.info(threadName + " [ThreadPoolTask] 自组网方案下发");
							handler = new PutAdhocSchemeConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网方案下发");
							break;

						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ADHOC_TIME_PLAN_CONFIG:
							log.info(threadName + " [ThreadPoolTask] 自组网时间计划下发");
							handler = new PutAdhocTimePlanConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网时间计划下发");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_PORTABLE_TIME_PLAN_CONFIG:
							log.info(threadName + " [ThreadPoolTask] 自组网时间计划下发");
							handler = new PutPortableTimePlanConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网时间计划下发");
							break;

						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ADHOC_HEART_BEAT:
							log.info(threadName + " [ThreadPoolTask] 自组网控制器心跳参数下发");
							handler = new PutAdhocHeartBeatHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网控制器心跳参数下发");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ADHOC_QUERY_INFO:
							log.info(threadName + " [ThreadPoolTask] 下放自组网获取设备信息");
							handler = new PutAdhocQueryInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 下放自组网获取设备信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ADHOC_FOLLOW_CONTROLLER_INFO:
							log.info(threadName + " [ThreadPoolTask] 下放自组网获取从控设备信息");
							handler = new PutAdhocFollowControllerInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 下放自组网获取从控设备信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_MAC_INFO:
							log.info(threadName + " [ThreadPoolTask] 自组网下放定位信号灯信息");
							handler = new PutMacInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网下放定位信号灯信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_DELETE_ADHOC_LIGHTINFO:
							log.info(threadName + " [ThreadPoolTask] 自组网下放删除灯信息");
							handler = new PutDeleteAdhocLightInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网下放删除灯信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ENDNETWORK_INFO:
							log.info(threadName + " [ThreadPoolTask] 自组网下放结束组网信息");
							handler = new PutEndNetworkInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网下放结束组网信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_STARTNETWORK_INFO:
							log.info(threadName + " [ThreadPoolTask] 自组网下放结束组网信息");
							handler = new PutStartNetworkInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网下放结束组网信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ENDSCANNER_INFO:
							log.info(threadName + " [ThreadPoolTask] 自组网下放结束扫描信息");
							handler = new PutEndScannerInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组下放结束扫描信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_MAGNETICLOCK_INFO:
							log.info(threadName + " [ThreadPoolTask] 机柜型故障检测器磁力锁远程控制信息开始");
							handler = new PutMagneticLockInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 机柜型故障检测器磁力锁远程控制信息结束");
							break;

						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ONCLICK_PARAMETER:
							log.info(threadName + " [ThreadPoolTask] 一键方案下发");
							handler = new PutOnclickParameterHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 一键方案下发");
							break;

						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_PORTABLE_TRAFFIC_QUERY_INFO:
							log.info(threadName + " [ThreadPoolTask] 下放移动式信号机获取从控设备信息");
							handler = new PutPortableTrafficQueryInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 下放移动式信号机获取从控设备信息");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_PORTABLE_TRAFFIC_MAC_INFO:
							log.info(threadName + " [ThreadPoolTask] 移动式信号灯下放定位信号灯信息");
//							handler = new PutPortableTrafficMacInfoHandle();
							handler = new PutPortableTrafficMacInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 移动式信号灯下放定位信号灯信息");
							break;
							//配置信控方案
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_PORTABLE_TRAFFIC_SCHEME_CONFIG:
							log.info(threadName + " [ThreadPoolTask] 移动式信号灯方案下发");
							handler = new PutPortableTrafficSchemeConfigHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 移动式信号灯方案下发");
							break;
							//统一所有方案
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_PORTABLE_SCHEME_LEVEL_INFO:
							log.info(threadName + " [ThreadPoolTask] 自组网方案下发");
							handler = new PutPortableSchemeLevelInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网方案下发");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_ADHOC_PORTABLE_SCHEME_LEVEL_INFO:
							log.info(threadName + " [ThreadPoolTask] 自组网方案下发");
							handler = new PutAdhocPortableSchemeLevelInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 自组网方案下发");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_DIMMING_PORTABLE_CONFIG:
							log.info(threadName + " [ThreadPoolTask] 调光方案下发");
							handler = new PutPortableDimmingHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 调光方案下发");
							break;
						case com.lumlux.signal.daemon.parser.heinqi.Constants.PACKET_TYPE.PUT_QUERY_ADHOC_INFO:
							log.info(threadName + " [ThreadPoolTask] 调光方案下发");
							handler = new PutQueryAdhocInfoHandler();
							handler.handle(receiveData, session);
							log.info(threadName + " [ThreadPoolTask] 调光方案下发");
							break;
					}
			}
		}
		log.debug(threadName + " [ThreadPoolTask] End Process: [sessionID] <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<" + session.getId());
    }
}
