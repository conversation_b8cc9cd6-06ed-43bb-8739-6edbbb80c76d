<div class="sketchMapWrapper">
   <div id="sketchMap" class="sketchMap" :style="'background-image: url(' + sketchMapImage + ')'" @drop.prevent="onSketchMapDrop" @dragover.prevent="onSketchMapDropOver">
       <div v-for="(component, i) in markedComponentsProxy" :id="'markedComponent_' + i" class="markedComponent" :style="calculateComponentPositionForDisplay(component)" draggable="true" @dragstart="onComponentDragStart($event, 'move')">
           <slot name="markedComponent" :component="component"></slot>
       </div>
   </div>
   <div class="componentLibraryWrapper" v-show="componentLibraryIsShown">
        <div v-if="componentLibraryTitle" class="componentLibraryTitle">
            {{componentLibraryTitle}}
        </div>
        <div class="componentLibrary">
            <div v-for="(e, i) in componentLibrary" :key="'libraryComponent_' + i" :id="'libraryComponent_' + i" class="libraryComponent" draggable="true" @dragstart="onComponentDragStart($event, 'copy')">
                <slot name="libraryComponent" :component="e"></slot>
            </div>
        </div>
   </div>
</div>

