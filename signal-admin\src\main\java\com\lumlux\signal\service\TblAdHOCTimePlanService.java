package com.lumlux.signal.service;

import com.heinqi.yangtes.jee.commons.service.CrudService;
import com.heinqi.yangtes.jee.commons.utils.IdGenUtil;
import com.heinqi.yangtes.jee.modules.sys.utils.UserUtils;
import com.lumlux.commons.dao.TblAdHOCTimePlanDao;
import com.lumlux.commons.entity.TblAdHOCTimePlan;
import com.lumlux.signal.entity.PutTimePlanBean;
//import com.lumlux.signal.entity.TimePlanDetailBean;
import com.lumlux.signal.entity.PutTimePlanDetailBean;
import com.lumlux.signal.util.Constant;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: TblAdHOCTimePlanService.java</p>
 * <p>Description: TblAdHOCTimePlanService单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2024/5/27</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
@Service
@Transactional(readOnly = true)
public class TblAdHOCTimePlanService extends CrudService<TblAdHOCTimePlanDao, TblAdHOCTimePlan> {

    @Autowired
    private TblAdHOCTimePlanDao timePlanDao;

    @Autowired
    private DaemonService daemonService;

    public void  save(TblAdHOCTimePlan timePlan){
        super.save(timePlan);
    }

    @Transactional(readOnly = false)
    public void insertTimePlan(PutTimePlanBean timePlanBean) {


//        List<TimePlanDetailBean> timePlanDetailBeans = timePlanBean.getTimePlanDetailBeans();

                TblAdHOCTimePlan timePlan = new TblAdHOCTimePlan();

                BeanUtils.copyProperties(timePlanBean, timePlan);
                timePlan.setId(IdGenUtil.uuid());
                timePlan.setCollectorId(timePlanBean.getId());
                timePlan.setCreateBy(UserUtils.getUser());
                timePlan.setCreateDate(new Date());
                timePlan.setUpdateDate(new Date());
                timePlan.setUpdateBy(UserUtils.getUser());
                timePlan.setRemarks("");
                timePlanDao.insert(timePlan);

        }

    @Transactional(readOnly = false)
    public TblAdHOCTimePlan selectTimePlanByPlanNoAndCollectorId(String collectorId, String controlType,String planNo,String schemeNo) {
        return timePlanDao.selectTimePlanByPlanNoAndCollectorId(collectorId,controlType,planNo,schemeNo);
    }
    @Transactional(readOnly = false)
    public void updateTimePlan(PutTimePlanBean timePlanBean) {
        List<PutTimePlanDetailBean> planDetailList = timePlanBean.getPlanDetailList();
        if (planDetailList != null && planDetailList.size() > 0) {
            for (PutTimePlanDetailBean planDetailBean : planDetailList){
                if (Constant.SCHEME_NUNBER_STATUS.HUANG_SHAN_SCHEME != Integer.parseInt(planDetailBean.getSchemeNo()) && Constant.SCHEME_NUNBER_STATUS.TOTAL_TURNOFF_SCHEME!= Integer.parseInt(planDetailBean.getSchemeNo())) {
                    TblAdHOCTimePlan timePlan = new TblAdHOCTimePlan();
                    BeanUtils.copyProperties(timePlanBean, timePlan);
//                    BeanUtils.copyProperties(timePlanDetailBean, timePlan);
                    timePlan.setUpdateDate(new Date());
                    timePlan.setUpdateBy(UserUtils.getUser());
                    timePlan.setRemarks("");
                    timePlanDao.update(timePlan);
                }
            }
        }

//            List<TimePlanDetailBean> timePlanDetailBeans = timePlanBean.getTimePlanDetailBeans();
//            if (timePlanDetailBeans != null && timePlanDetailBeans.size() > 0) {


//                for (TimePlanDetailBean timePlanDetailBean : timePlanDetailBeans) {


//                }
//            }

    }
    @Transactional(readOnly = false)
    public void putTimePlanInfo(int count,String code, PutTimePlanBean timePlanBean,String model) {

        daemonService.putTimePlanInfo(code, count,timePlanBean,model);


    }
    @Transactional(readOnly = false)
    public void deleteTimePlan(String collectorId, String controlType,String planNo,String schemeNo) {
        timePlanDao.deleteTimePlan(collectorId,controlType,planNo,schemeNo);

    }
    @Transactional(readOnly = false)
    public void updateEnableTimePlan(String collectorId,String controlType, String planNo,String schemeNo, String enabled) {
        timePlanDao.updateEnableTimePlan(collectorId,controlType,planNo,schemeNo,enabled);

    }
    @Transactional(readOnly = false)
    public List<TblAdHOCTimePlan> selectAllTimePlan(String collectorId) {
        return timePlanDao.selectAllTimePlan(collectorId);
    }
    @Transactional(readOnly = false)
    public Integer selectTimePlanTotal(String collectorId, String planNo) {
        return timePlanDao.selectTimePlanTotal(collectorId,planNo);
    }
}
