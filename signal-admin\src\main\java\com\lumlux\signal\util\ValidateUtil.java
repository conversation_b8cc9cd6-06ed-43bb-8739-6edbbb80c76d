package com.lumlux.signal.util;


import com.lumlux.signal.entity.AlterableLightValueBean;
import com.lumlux.signal.entity.SpecificLightValueBean;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * <p>Title: ValidateUtil.java</p>
 * <p>Description: ValidateUtil单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2024/6/20</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class ValidateUtil <T>{
    private static Validator validator;

    static {
        /**
         * 使用hibernate的注解来进行验证 failFast true仅仅返回第一条错误信息 false返回所有错误
         */
        validator = Validation
                .byProvider(HibernateValidator.class).configure().failFast(false).buildValidatorFactory().getValidator();

    }

    /**
     * @param obj 校验的对象
     * @param <T>
     * @return
     */
    public static <T> Set<ConstraintViolation<T>> validate(T obj, Class... groupClasses) {
        Set<ConstraintViolation<T>> constraintViolations;
        if(groupClasses != null && groupClasses.length>0){
            constraintViolations = validator.validate(obj,groupClasses);
        }else {
            constraintViolations = validator.validate(obj);
        }
        return constraintViolations;
    }


    /**
     * 校验列表中所有元素的 String 类型参数是否全部为 "0"
     * @param list 待校验的列表
     * @param stringExtractor 提取 String 参数的函数
     * @param <T> 列表元素的类型
     * @return 如果所有元素的指定参数都为 "0"，返回 true；否则返回 false
     */
    public static <T> boolean checkAllStringFieldsZero(List<T> list, Function<T, String> stringExtractor) {
        return list.stream().allMatch(item -> "0".equals(stringExtractor.apply(item)));
    }


    /**
     * 校验列表中每个后续对象的 String 参数是否大于前一个对象的总周期参数
     * @param specificLightValueBean 待校验的列表
     * @return 如果所有后续对象的 String 参数都大于前一个对象的总周期参数，返回 true；否则返回 false
     */
    public static  boolean checkStringLessThanPreviousTotalPeriod(
            SpecificLightValueBean specificLightValueBean) {

        String yellowLight = specificLightValueBean.getYellowLight();
        String wholeRed = specificLightValueBean.getWholeRed();
        List<AlterableLightValueBean> lightValueBeans = specificLightValueBean.getLightValueBeans();
        if (lightValueBeans == null || lightValueBeans.size() <= 0) {
            return true; // 空列表退出
        }
        for (int i = 2; i < lightValueBeans.size(); i++) {
          Double  previous1 = Double.parseDouble(lightValueBeans.get(0).getGreenStartTime())+Double.parseDouble(lightValueBeans.get(0).getFrequentGreen())+Double.parseDouble(yellowLight)+Double.parseDouble(wholeRed);
            Double  previous2 = Double.parseDouble(lightValueBeans.get(1).getGreenStartTime())+Double.parseDouble(lightValueBeans.get(1).getFrequentGreen())+Double.parseDouble(yellowLight)+Double.parseDouble(wholeRed);
            Double current = Double.parseDouble(lightValueBeans.get(i).getGreenStartTime());
            // 处理 null 值
            if (previous1 == null || current == null || previous2 ==null) {
                return true; // 不允许 null 值
            }

                if (previous1 > current && previous2 > current) {
                    return true; // 发现满足条件的元素对
                }
        }
        return false; // 所有元素都不满足条件
    }

    /**
     * 将字符串解析为 double，支持常见的数值格式
     */
    private static double parseStringToDouble(String value) throws NumberFormatException {
        // 移除可能的千位分隔符和空格
        String cleanedValue = value.replace(",", "").trim();

        // 尝试解析为 double
        return Double.parseDouble(cleanedValue);
    }


}
