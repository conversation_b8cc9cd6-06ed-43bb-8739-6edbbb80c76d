.signalTimingSlider .el-slider {
    pointer-events: none;
}

.signalTimingSlider .el-slider__button-wrapper {
    pointer-events: auto;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.signalTimingSlider .el-slider__button-wrapper.hover {
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
}

.signalTimingSlider .el-slider__button-wrapper:active {
    opacity: 1;
}

.signalTimingSlider .el-slider__button:active::before {
    display: initial;
}

/* .signalTimingSlider .el-slider__button-wrapper::before {
    content: "";
    border: 1px green dashed;
    height: 200vh;
    position: absolute;
    left: calc(50% - 1px);
    top: -100vh;
    display: none;
}

.signalTimingSlider .el-slider__button-wrapper.hover::before {
    content: "";
    border: 1px green dashed;
    height: 200vh;
    position: absolute;
    left: calc(50% - 1px);
    top: -100vh;
    display: initial;
} */

.signalTimingSlider .el-slider__button {
    border-color: green;
}

.signalTimingSlider .el-slider__runway {
    background-color: red;
    border-radius: 0;
}

.signalTimingSlider .el-slider__bar {
    background-color: green;
    border-radius: 0;
}

.signalTimingSlider .el-slider__bar::before {
    content: "";
    background-color: white;
    width: 2px;
    height: 6px;
    display: inline-block;
    position: absolute;
    left: -1px;
}

.signalTimingSlider .sliderDiv {
    position: relative;
    margin: 30px 0 30px 0;
}

.signalTimingSlider .extraBarWrapper {
    height: 38px;
    position: absolute;
    top: 0px;
    display: flex;
    align-items: center;
}

.signalTimingSlider .extraBar {
    height: 6px;
    width: 100%;
}

.signalTimingSlider .barBackground {
    background-color: white;
}

.signalTimingSlider .greenBar {
    background-color: green;
    border-left: 2px white solid;
}

.signalTimingSlider .yellowBar {
    background-color: rgb(247, 181, 0);
    border-right: 1px white solid;
    border-left: 2px white solid;
}

.signalTimingSlider .allRedBar {
    background-color: red;
    border-left: 1px white solid;
    border-right: 2px white solid;
}

.signalTimingSlider .fixRedBar {
    background-color: red;
}

.signalTimingSlider .barValueToolTip {
    color: white;
    border-radius: 5px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
    height: 15px;
    font-size: 12px;
    position: absolute;
    transform: translateX(-50%);
    z-index: 1001;
    user-select: none;
}

.signalTimingSlider .greenBarValueToolTip {
    background-color: green;
}

.signalTimingSlider .redBarValueToolTip {
    background-color: red;
    bottom: 100%;
}

.signalTimingSlider .yellowBarValueToolTip {
    background-color: rgb(247, 160, 0);
    bottom: 100%;
}

.signalTimingSlider .barValueToolTip::after {
    content: "";
    position: absolute;
    border: 5px solid transparent;
}

.signalTimingSlider .greenBarValueToolTip::after {
    border-bottom-color: green;
    bottom: 25px;
}

.signalTimingSlider .redBarValueToolTip::after {
    border-top-color: red;
    top: 25px;
}

.signalTimingSlider .yellowBarValueToolTip::after {
    border-top-color: rgb(247, 160, 0);
    top: 25px;
}

.signalTimingSlider .alignDashLine {
    border: 1px green dashed;
    position: absolute;
    bottom: 0;
    top: 0;
    transition: opacity 0.2s ease-in-out;
    z-index: 1;
}