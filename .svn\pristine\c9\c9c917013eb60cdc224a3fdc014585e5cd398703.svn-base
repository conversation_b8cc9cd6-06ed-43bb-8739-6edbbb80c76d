const sketchMapScript = {
    props: {
        sketchMapImage: { type: String },
        componentLibraryTitle: { type: String },
        componentLibraryIsShown: {type: Boolean, default: true},
        componentLibrary: { type: Array },
        markedComponents: { type: Array }
    },
    computed: {
        draggedItemIsLibraryComponent() {
            return this.draggedComponentId.indexOf("libraryComponent_") >= 0;
        },
        draggedComponentIndex() {
            return this.draggedItemIsLibraryComponent ? "" : this.draggedComponentId.split("_")[1];
        },
        draggedComponentClientRects() {
            return this.draggedComponentId != "" && document.getElementById(this.draggedComponentId).getClientRects()[0];
        },
        dragStartMousePositionXX() {
            return {
                x: this.dragStartMousePosition.x * (1 / window.devicePixelRatio),
                y: this.dragStartMousePosition.y * (1 / window.devicePixelRatio)
            }
        }
    },
    mounted() {
        this.placeholderImage.src = "data:image/gif;base64," + "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
        this.preventAllDocumentDefaultDragBehavior(window.top.document);
        window.onresize = this.calculateSketchMapClientRects
        document.documentElement.style.setProperty('--dynamic-content', 'AAAAA');
    },
    methods: {
        onSketchMapDropOver(e) {
            if (!this.draggedItemIsLibraryComponent) {
                this.$set(this.markedComponentsProxy, this.draggedComponentIndex, this.calculateComponentPosition(e.clientX, e.clientY));
            }
        },
        onSketchMapDrop(e) {
            e.dataTransfer.dropEffect = e.dataTransfer.effectAllowed;
            // if (this.draggedItemIsLibraryComponent && this.markedComponentsProxy.find(component => component.no == this.newComponentNo)) {

            if (this.draggedItemIsLibraryComponent) {
                this.$set(this.markedComponentsProxy, this.markedComponentsProxy.length, this.calculateComponentPosition(e.clientX, e.clientY));
                console.log(this.newComponentNo)
                this.$emit('sketchMapDrop', this.newComponentNo)
            }
        },
        // Just For Chrome/Edge, Use Together With Event 'ondrag'
        // onComponentDrag(e) {
        //   this.$set(this.markedComponentsProxy, this.draggedComponentIndex, {
        //     x: e.clientX - e.layerX,
        //     y: e.clientY - e.layerY
        //   });
        // },
        onComponentDragStart(event, effectAllowed) {
            console.log(3)
            this.draggedComponentId = event.srcElement.id || event.srcElement.parentElement.id;
            this.dragStartMousePosition.x = event.offsetX;
            this.dragStartMousePosition.y = event.offsetY;
            event.dataTransfer.effectAllowed = effectAllowed;
            if (!this.draggedItemIsLibraryComponent)
                event.dataTransfer.setDragImage(this.placeholderImage, 0, 0);
            this.unhighlightElements(window.top.document);
        },

        addNewComponent(no, doc, highlightedElements) {
            this.highlightElements(doc, highlightedElements)
            this.newComponentNo = no
        },

        calculateComponentPositionForDisplay(component) {
            return 'left:calc(' + (component.x * this.sketchMapClientRects.innerWidth / this.sketchMapClientRects.width) + '% - ' + component.width/2 + 'px + ' + this.sketchMapClientRects.innerLeft + 'px);' +
                'top:calc(' + (component.y * this.sketchMapClientRects.innerHeight / this.sketchMapClientRects.height) + '% - ' + component.height/2 + 'px + ' + this.sketchMapClientRects.innerTop + 'px);' +
                'scale: ' + (1 / window.devicePixelRatio);
        },
        calculateComponentPosition(clientX, clientY) {
            // innerBorder -> draggedItemPosition
            let draggedItemOffsetX = clientX - this.sketchMapClientRects.left - this.sketchMapClientRects.innerLeft - this.dragStartMousePosition.x;
            let draggedItemOffsetY = clientY - this.sketchMapClientRects.top - this.sketchMapClientRects.innerTop - this.dragStartMousePosition.y;

            return {
                x: (draggedItemOffsetX < 0
                        ? (this.draggedComponentClientRects.width / 2)
                        : draggedItemOffsetX + this.draggedComponentClientRects.width > this.sketchMapClientRects.innerWidth
                            ? (this.sketchMapClientRects.innerWidth - this.draggedComponentClientRects.width / 2)
                            : (draggedItemOffsetX + this.draggedComponentClientRects.width / 2)
                ) / this.sketchMapClientRects.innerWidth * 100,
                y: (draggedItemOffsetY < 0
                        ? (this.draggedComponentClientRects.height / 2)
                        : draggedItemOffsetY + this.draggedComponentClientRects.height > this.sketchMapClientRects.innerHeight
                            ? (this.sketchMapClientRects.innerHeight - this.draggedComponentClientRects.height / 2)
                            : (draggedItemOffsetY + this.draggedComponentClientRects.height / 2)
                ) / this.sketchMapClientRects.innerHeight * 100,
                width: this.draggedComponentClientRects.width,
                height: this.draggedComponentClientRects.height
            };
        },
        calculateSketchMapClientRects() {
            let tempImg = new Image();
            let sketchMapSize = this.draggedComponentId != "" && document.getElementById("sketchMap").getClientRects()[0]

            tempImg.onload = _ => {
                let zoomScale = Math.min(sketchMapSize.width / tempImg.width, sketchMapSize.height / tempImg.height)
                sketchMapSize.innerWidth = tempImg.width * zoomScale
                sketchMapSize.innerHeight = tempImg.height * zoomScale
                sketchMapSize.innerLeft = (sketchMapSize.width - sketchMapSize.innerWidth) / 2
                sketchMapSize.innerTop = (sketchMapSize.height - sketchMapSize.innerHeight) / 2
                this.sketchMapClientRects = sketchMapSize
            }

            tempImg.src = this.sketchMapImage;
        },
        preventAllDocumentDefaultDragBehavior(doc) {
            this.traverseAllIframes(doc, doc => {
                doc.body.ondrop = this.preventDocumentDefaultDragBehavior
            })
        },
        highlightElements(doc, highlightedElements) {
            this.insertNewCssRule(doc, '.sketchMap-aboveMask', this.aboveMaskStyle)
            for (const e of highlightedElements) {
                e.className += ' sketchMap-aboveMask'
                this.elementsAffectedByHighlightFunction.aboveMasks.push(e)
            }

            this.traverseAllIframes(doc, doc => {
                this.insertNewCssRule(doc, '.sketchMap-mask', this.maskStyle)
                let mask = doc.createElement('div')
                mask.className += ' sketchMap-mask'
                this.elementsAffectedByHighlightFunction.masks.push(mask)
                doc.body.appendChild(mask)
            }, iframe => {
                this.insertNewCssRule(iframe.contentDocument, '.sketchMap-aboveMask', this.aboveMaskStyle)
                iframe.className += ' sketchMap-aboveMask'
            })
        },
        unhighlightElements(doc) {
            this.traverseAllIframes(doc, doc => {
                for (const mask of this.elementsAffectedByHighlightFunction.masks)
                    if(mask.ownerDocument == doc)
                        doc.body.removeChild(mask)
            }, iframe => {
                iframe.className = iframe.className.replace(' sketchMap-aboveMask', '')
                for (const aboveMask of this.elementsAffectedByHighlightFunction.aboveMasks)
                    if(aboveMask.ownerDocument == doc)
                        aboveMask.className = aboveMask.className.replace(' sketchMap-aboveMask', '')
            })
            this.elementsAffectedByHighlightFunction.masks = []
            this.elementsAffectedByHighlightFunction.aboveMasks = []
        },
        insertNewCssRule(doc, cssRuleName, cssRuleContent) {
            if(!doc.getElementsByTagName('style').length) doc.appendChild(doc.createElement('style'))
            let classes = doc.getElementsByTagName('style')[0].sheet
            for (const k in classes.cssRules)
                if(classes.cssRules[k].selectorText == cssRuleName) return
            classes.insertRule(cssRuleContent, classes.cssRules.length)
        },
        traverseAllIframes(doc, toDo, preNext = ()=>{}) {
            toDo(doc)
            let innerIframes = doc.getElementsByTagName("iframe")
            for (const k in Object.keys(innerIframes)) {
                preNext(innerIframes[k])
                this.traverseAllIframes(innerIframes[k].contentDocument, toDo, preNext)
            }
        }
    },
    data() {
        return {
            tempUrl: "https://76o38818t8.goho.co/adHoc/resources/image/adHoc/left.png",
            maskStyle: '.sketchMap-mask {position: absolute; top: 0; left: 0; width: 100vw; height: 100vh; background-color: #000000AA; z-index:10;}',
            aboveMaskStyle: '.sketchMap-aboveMask {position: relative; z-index: 11;}',
            sketchMapClientRects: {},
            dragStartMousePosition: { x: 0, y: 0 },
            draggedComponentId: {},
            markedComponentsProxy: this.markedComponents,
            isDragging: false,
            placeholderImage: new Image(0, 0),
            elementsAffectedByHighlightFunction: { masks: [], aboveMasks: [] },

            newComponentNo: 0,
        };
    },
    watch: {
        sketchMapImage() {
           this.calculateSketchMapClientRects()
        },
        markedComponents(v) {
            this.markedComponentsProxy = v
        },
        markedComponentsProxy(v) {
            this.$emit('update:markedComponents', v)
        }
    }
};