package com.lumlux.signal.service;

import com.heinqi.yangtes.jee.commons.service.CrudService;
import com.heinqi.yangtes.jee.commons.utils.IdGenUtil;
import com.heinqi.yangtes.jee.modules.sys.entity.User;
import com.heinqi.yangtes.jee.modules.sys.utils.UserUtils;
import com.lumlux.commons.dao.*;
import com.lumlux.commons.entity.TblAdHOCScheme;
import com.lumlux.commons.entity.TblAdHOCTimePlan;
import com.lumlux.commons.entity.TblSignalLight;
import com.lumlux.commons.entity.TblSignalLightDetail;
import com.lumlux.signal.entity.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Date;
import java.util.List;

@Service
@Transactional(readOnly = true)
public class TblAdHOCSchemeService extends CrudService<TblAdHOCSchemeDao, TblAdHOCScheme> {

	@Autowired
	private TblAdHOCSchemeDao tblAdHOCSchemeDao;

	@Autowired
	private TblAdHOCLightDetailDao lightDetailDao;

	@Autowired
	private DaemonService daemonService;
	@Autowired
	private TblCollectorDao collectorDao;
	@Autowired
	private TblAdHOCTimePlanDao adHOCTimePlanDao;
	@Autowired
	private TblAdHOCDao tblAdHOCDao;

	private String schemeNo;

	public String getSchemeNo() {
		return schemeNo;
	}

	public void setSchemeNo(String schemeNo) {
		this.schemeNo = schemeNo;
	}

	@Transactional(readOnly = false)
	public void  save(TblAdHOCScheme adHOCScheme){
		super.save(adHOCScheme);
	}
	@Transactional(readOnly = false)
	public  int countBySchemeNoAndCollectorId(String collectorId,String schemeNo) {
		return tblAdHOCSchemeDao.countBySchemeNoAndCollectorId(collectorId,schemeNo);
	}

	@Transactional(readOnly = false)
	public int selectSchemeCount(String collectorId) {
		return tblAdHOCSchemeDao.selectSchemeCount(collectorId);
	}

	@Transactional(readOnly = false)
	public void updateAdHOCScheme(TblAdHOCScheme tblAdHOCScheme) {
		tblAdHOCSchemeDao.updateAdHOCSchemeBySchemeNoAndCollector(tblAdHOCScheme);
	}
	@Transactional(readOnly = false)
	public TblAdHOCScheme getAdHOCScheme(String schemeNo, String id) {
		return tblAdHOCSchemeDao.getAdHOCScheme(schemeNo, id);
	}

	@Transactional(readOnly = false)
    public Integer selectAdHOCSchemeByCollectorIdAndSchemeNo(String collectorId, String schemeNo) {
		return tblAdHOCSchemeDao.selectAdHOCSchemeByCollectorIdAndSchemeNo(collectorId, schemeNo);
    }
	@Transactional(readOnly = false)
	public void insertScheme(AddSpecificLightValueBean addSpecificLightValueBean) {

		   //方案添加
        TblAdHOCScheme tblAdHOCScheme = new TblAdHOCScheme();
        BeanUtils.copyProperties(addSpecificLightValueBean, tblAdHOCScheme);
        tblAdHOCScheme.setId(IdGenUtil.uuid());
		tblAdHOCScheme.setCollectorId(addSpecificLightValueBean.getId());
        tblAdHOCScheme.setEnable("1");
        tblAdHOCScheme.setMaxGreen(addSpecificLightValueBean.getMaxGreen());
        tblAdHOCScheme.setCreateBy(UserUtils.getUser());
        tblAdHOCScheme.setCreateDate(new Date());
        tblAdHOCScheme.setUpdateDate(new Date());
        tblAdHOCScheme.setUpdateBy(UserUtils.getUser());
        tblAdHOCSchemeDao.insert(tblAdHOCScheme);

		List<TblSignalLight> lightList = tblAdHOCDao.getLightByCollectorId(addSpecificLightValueBean.getId());
		if (lightList != null && lightList.size() > 0) {
			for (TblSignalLight light : lightList) {
				TblSignalLightDetail lightDetail = new TblSignalLightDetail();
				lightDetail.setId(IdGenUtil.uuid());
				lightDetail.setSightLightId(light.getId());
				lightDetail.setCollectorId(addSpecificLightValueBean.getId());
				lightDetail.setSchemeNo(addSpecificLightValueBean.getSchemeNo());
				lightDetail.setLightNumber(light.getLightNumber());
				lightDetail.setFrequentGreen("10");
				lightDetail.setRedTime("5");
				lightDetail.setGreenTime("5");
				lightDetail.setGreenStartTime("0");
				lightDetail.setEnabled("1");
				lightDetail.setCreateBy(UserUtils.getUser());
				lightDetail.setCreateDate(new Date());
				lightDetail.setUpdateDate(new Date());
				lightDetail.setUpdateBy(UserUtils.getUser());
				lightDetailDao.insert(lightDetail);
			}
		}
	}
	@Transactional(readOnly = false)
	public void updateSchemeInfo(AddSpecificLightValueBean addSpecificLightValueBean) {

		// 更新方案信息
		TblAdHOCScheme tblAdHOCScheme = new TblAdHOCScheme();
		BeanUtils.copyProperties(addSpecificLightValueBean, tblAdHOCScheme);
		tblAdHOCScheme.setUpdateDate(new Date());
		tblAdHOCScheme.setUpdateBy(UserUtils.getUser());
		tblAdHOCScheme.setEnable("1");
		tblAdHOCScheme.setCollectorId(addSpecificLightValueBean.getId());
		tblAdHOCSchemeDao.update(tblAdHOCScheme);

		List<AlterableLightValueBean> lightValueBeans = addSpecificLightValueBean.getLightValueBeans();
		if (lightValueBeans != null && lightValueBeans.size() > 0){
			for (AlterableLightValueBean lvBean : lightValueBeans) {
			TblSignalLightDetail lightDetail = new TblSignalLightDetail();
			BeanUtils.copyProperties(lvBean, lightDetail);
			lightDetail.setCollectorId(addSpecificLightValueBean.getId());
			lightDetail.setSchemeNo(addSpecificLightValueBean.getSchemeNo());
			lightDetail.setEnabled("1");
			lightDetail.setUpdateDate(new Date());
			lightDetail.setUpdateBy(UserUtils.getUser());
//			lightDetail.setLightStatus(lvBean.getLightStatus());
			lightDetailDao.update(lightDetail);

			}
		}

	}

	@Transactional(readOnly = false)
	public void deleteSchemeInfo(String collectorId, String schemeNo) {

		tblAdHOCSchemeDao.deleteSchemeInfo(collectorId, schemeNo);

		List<TblAdHOCTimePlan> tblAdHOCTimePlans = adHOCTimePlanDao.selectTimePlanByCollectorId(collectorId,schemeNo);
		if (tblAdHOCTimePlans.size()>0 && tblAdHOCTimePlans != null){
			for (TblAdHOCTimePlan tblAdHOCTimePlan : tblAdHOCTimePlans){
				adHOCTimePlanDao.deleteTimePlanByIdAndSchemeNo(tblAdHOCTimePlan.getId(),schemeNo);
			}
			}

		List<TblSignalLightDetail> lightDetailByCollectorIdAndSchemeNo = lightDetailDao.getLightDetailByCollectorIdAndSchemeNo(collectorId,schemeNo);
		if (lightDetailByCollectorIdAndSchemeNo.size()>0 && lightDetailByCollectorIdAndSchemeNo != null) {
			for (TblSignalLightDetail lightDetail : lightDetailByCollectorIdAndSchemeNo) {
				lightDetailDao.deleteLightDetailByIdAndSchemeNo(collectorId,schemeNo);
			}
		}




	}
	@Transactional(readOnly = false)
	public List<TblAdHOCScheme> getSchemeInfo(String collectorId, String schemeNo) {
		return tblAdHOCSchemeDao.getSchemeInfo(collectorId, schemeNo);
	}
}