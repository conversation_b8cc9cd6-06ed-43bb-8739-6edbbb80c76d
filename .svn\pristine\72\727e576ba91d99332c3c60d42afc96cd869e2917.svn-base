<div class="signalTimingSlider">
    <div class="sliderDiv">
        <el-slider :value="greenBarValue" range :show-tooltip="false" :max="cycleDuration" :min="0" @input="onSliderInput">
        </el-slider>
        <div class="extraBarWrapper" :style="greenBarWidth + greenBarPosition">
            <div class="extraBar greenBar"></div>
        </div>
        <div class="extraBarWrapper" :style="barBackgroundWidth + barBackgroundPosition">
            <div class="extraBar barBackground"></div>
        </div>
        <div class="extraBarWrapper" :style="yellowBarWidth + yellowBarPosition">
            <div class="extraBar yellowBar" :style="yellowBarBorderIsShown"></div>
        </div>
        <div class="extraBarWrapper" :style="allRedBarWidth + allRedBarPosition">
            <div class="extraBar allRedBar" :style="allRedBarBorderIsShown"></div>
        </div>
        <div class="extraBarWrapper" :style="suffixRedBarWidth + suffixRedBarPosition">
            <div class="extraBar fixRedBar"></div>
        </div>
        <div class="extraBarWrapper" :style="prefixRedBarWidth + prefixRedBarPosition">
            <div class="extraBar fixRedBar" style="border-right: 2px white solid;"></div>
        </div>
        <div class="barValueToolTip greenBarValueToolTip" :style="greenBarStartPointValueToolTipPosition" v-show="!greenBarIsNarrow">
            {{greenStartPointProxy}}
        </div>
        <div class="barValueToolTip greenBarValueToolTip" :style="greenBarEndPointValueToolTipPosition" v-show="!greenBarIsNarrow">
            {{greenEndPoint}}
        </div>
        <div class="barValueToolTip greenBarValueToolTip" :style="greenBarStart2EndValueToolTipPosition" v-show="greenBarIsNarrow">
            {{greenStartPointProxy + "→" +greenEndPoint}}
        </div>
        <div class="barValueToolTip" :class="extraBarValueToolTipClass" :style="extraBarEndPointValueToolTipPosition" v-show="extraBarValueToolTipIsShown">
            {{allRedEndPoint}}
        </div>
    </div>
    <div class="alignDashLine" :style="alignDashLinePosition + alignDashLineDisplay"></div>
</div>
