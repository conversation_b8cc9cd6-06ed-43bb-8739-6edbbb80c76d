package com.lumlux.signal.daemon.parser.heinqi;

import com.lumlux.signal.daemon.mina.SignalSessionIdManager;
import com.lumlux.signal.daemon.parser.AbstractHandler;
import com.lumlux.signal.daemon.parser.hongdian.HongdianUtil;
import com.lumlux.signal.daemon.util.ByteUtil;
import org.apache.log4j.Logger;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;

/**
 *移动信号灯时间计划
 */
public class PutPortableTimePlanConfigHandler extends AbstractHandler {

	private transient Logger log = Logger.getLogger(PutPortableTimePlanConfigHandler.class);

	@Override
	public int handle(final byte[] data, final IoSession session) {
		String threadName = Thread.currentThread().getName();
		byte[] baImei = new byte[11];
		//6b 1a 31 38 39 31 36 31 31 32 38 30 32 02 6b
		System.arraycopy(data, 2, baImei, 0, 11);
		String strBaImei = new String(baImei);
		log.info(threadName + " [PutPortableTimePlanConfigHandler] device's baIme :" + strBaImei);
		Long sessionId = SignalSessionIdManager.getSessionId(strBaImei);
		if (sessionId != null) {
			IoSession sendSession = session.getService().getManagedSessions().get(sessionId);
			if (sendSession != null) {
				log.debug(threadName + " [PutPortableTimePlanConfigHandler] 下放自组网时段计划[session id :" + sessionId + "]");
				byte[] innerContent = new byte[] { (byte) 0xaa, (byte)0xff,data[1] , 0x00,0x1d,0x01, data[13],data[14],data[15], data[16], data[17],
						data[18], data[19], data[20], data[21],data[22], data[23],data[24],data[25],data[26],data[27],data[28],data[29],data[30],data[31],data[32],data[33],data[34],data[35],data[36],
						0x00, 0x00, 0x00,0x00,(byte) 0xaa };
				byte[] sendData = HongdianUtil.generateHongdianSendContent(innerContent, baImei);
				log.info(threadName + " [PutPortableTimePlanConfigHandler] 发送数据 : " + ByteUtil.asHex(sendData));
				sendSession.write(IoBuffer.wrap(sendData));
			}
		}
		return 0;
	}
}
