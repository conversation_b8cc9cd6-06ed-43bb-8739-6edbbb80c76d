const signalTimingSliderScript = {
    props: {
        greenStartPoint: { type: Number },
        greenDuration: { type: Number },
        maxGreen: { type: Number },
        cycleDuration: { type: Number },
        allRedDuration: { type: Number },
        yellowDuration: { type: Number }
    },
    computed: {
        greenEndPoint() {
            console.log('greenEndPoint:', this.greenStartPointProxy, this.greenDurationProxy,this.greenStartPointProxy + this.greenDurationProxy)
            return this.greenStartPointProxy + this.greenDurationProxy;
        },
        yellowEndPoint() {
            return this.greenEndPoint + this.yellowDuration;
        },
        allRedEndPoint() {
            return this.yellowEndPoint + this.allRedDuration;
        },
        greenBarValue: {
            set(v) {

                console.log('greenBarValue-Set:', v)
                this.greenStartPointProxy = v[0];
                this.greenDurationProxy = v[1] - v[0];
                this.$emit('update:greenStartPoint', v[0]);
                this.$emit('update:greenDuration', v[1] - v[0]);
            },
            get() {
                console.log('greenBarValue-Get:', [this.greenStartPointProxy, this.greenEndPoint])
                return [this.greenStartPointProxy, this.greenEndPoint];
            }
        },
        greenBarWidth() {
            return (
                "width: calc((100% / " +
                this.cycleDuration +
                " * " +
                this.greenDurationProxy +
                ") - 0px);"
            );
        },
        yellowBarWidth() {
            return (
                "width:" +
                (this.yellowDuration
                    ? "calc((100% / " +
                    this.cycleDuration +
                    " * " +
                    this.yellowDuration +
                    ") + 1px);"
                    : "0px;")
            );
        },
        allRedBarWidth() {
            return (
                "width: calc(100% / " +
                this.cycleDuration +
                " * " +
                this.allRedDuration +
                ");"
            );
        },
        barBackgroundWidth() {
            return (
                "width: calc((100% / " +
                this.cycleDuration +
                " * " +
                (this.yellowDuration + this.allRedDuration) +
                ")" +
                (this.yellowDuration ? " + 1px" : "") +
                ");"
            );
        },
        suffixRedBarWidth() {
            return (
                "width: calc(100% / " +
                this.cycleDuration +
                " * " +
                (this.cycleDuration - this.yellowDuration - this.allRedDuration - this.greenDurationProxy - this.greenStartPointProxy) +
                ");"
            );
        },
        prefixRedBarWidth() {
            return (
                "width: calc((100% / " +
                this.cycleDuration +
                " * " +
                this.greenStartPointProxy +
                ") + 1px);"
            );
        },
        greenBarPosition() {
            return (
                "left: calc((100% / " +
                this.cycleDuration +
                " * " +
                this.greenStartPoint +
                ") - 1px);"
            );
        },
        barBackgroundPosition() {
            return (
                "left: calc((100% / " +
                this.cycleDuration +
                " * " +
                this.greenEndPoint +
                ")" +
                (this.yellowDuration ? " - 1px" : "") +
                ");"
            );
        },
        yellowBarPosition() {
            return (
                "left: calc((100% / " +
                this.cycleDuration +
                " * " +
                this.greenEndPoint +
                ") - 1px);"
            );
        },
        allRedBarPosition() {
            return (
                "left: calc(100% / " +
                this.cycleDuration +
                " * " +
                this.yellowEndPoint +
                ");"
            );
        },
        prefixRedBarPosition() {
            return (
                "left: 0;"
            );
        },
        suffixRedBarPosition() {
            return (
                "left: calc(100% / " +
                this.cycleDuration +
                " * " +
                this.allRedEndPoint +
                ");"
            );
        },
        extraBarIsNarrow() {
            return (
                (this.yellowDuration &&
                    this.cycleDuration / this.yellowDuration > 100) ||
                (this.allRedDuration && this.cycleDuration / this.allRedDuration > 100)
            );
        },
        yellowBarBorderIsShown() {
            return this.extraBarIsNarrow
                ? "border-width: 0px;"
                : this.yellowDuration == 0
                    ? "border-left-width: 0px;"
                    : "";
        },
        allRedBarBorderIsShown() {
            return this.allRedDuration == 0 || this.extraBarIsNarrow
                ? "border-left-width: 0px;"
                : "";
        },
        greenBarStartPointValueToolTipPosition() {
            return (
                "left: calc(100% / " +
                this.cycleDuration +
                " * " +
                this.greenStartPointProxy +
                ");"
            );
        },
        greenBarEndPointValueToolTipPosition() {
            return (
                "left: calc(100% / " +
                this.cycleDuration +
                " * " +
                this.greenEndPoint +
                ");"
            );
        },
        greenBarStart2EndValueToolTipPosition() {
            return (
                "left: calc(100% / " +
                this.cycleDuration +
                " * (" +
                (this.greenStartPointProxy + this.greenDurationProxy / 2) +
                "));"
            );
        },
        extraBarEndPointValueToolTipPosition() {
            return (
                "left: calc(100% / " +
                this.cycleDuration +
                " * " +
                this.allRedEndPoint +
                ");"
            );
        },
        greenBarIsNarrow() {
            return this.cycleDuration / this.greenDurationProxy > 20;
        },
        extraBarValueToolTipClass() {
            return (this.allRedDuration ? "red" : "yellow") + "BarValueToolTip";
        },
        extraBarValueToolTipIsShown() {
            return this.yellowDuration || this.allRedDuration;
        },
        alignDashLinePosition() {
            return "left: " + (this.signalTimingSliderOffsetWidth / this.cycleDuration * 
                (this.activeHandleIndex == -1 ? 10000 :([this.greenStartPointProxy, this.greenEndPoint][this.activeHandleIndex])) +
                this.signalTimingSliderOffsetLeft - 1) + ";";
        },
        alignDashLineDisplay() {
            return this.displayedAlignDashLine ? "" : "opacity: 0;";
        }
    },
    mounted() {
        this.$el.querySelectorAll('.el-slider__button-wrapper').forEach((item, index) => {
            item.addEventListener('mouseenter', () => this.displaySignalTimingSlider(index))
            item.addEventListener('mousedown', () => {
                console.log(item,index)
                this.activeHandleIndex = index
                this.preGreenBarValue = this.greenBarValue
                this.activeSlid = this.$el.querySelectorAll('.el-slider__button-wrapper.hover')[0]
                this.displaySignalTimingSlider(index)
            })
            item.addEventListener('mouseleave', () => this.hideSignalTimingSlider(index))
        })
        document.addEventListener('mouseup', () => {this.hideSignalTimingSlider();this.preGreenBarValue = null;this.isReverse = false})
        this.getSignalTimingSliderPosition()
    },
    data() {
        return {
            greenStartPointProxy: this.greenStartPoint,
            greenDurationProxy: this.greenDuration,

            preGreenBarValue: null,

            displayedAlignDashLine: 0,
            activeHandleIndex: -1,

            activeSlid: null,
            isReverse: false,

            signalTimingSliderOffsetWidth: 0,
            signalTimingSliderOffsetLeft: 0
        };
    },
    methods: {
        getSignalTimingSliderPosition() {
            this.signalTimingSliderOffsetWidth = this.$el.querySelector(".sliderDiv").offsetWidth;
            this.signalTimingSliderOffsetLeft = this.$el.querySelector(".sliderDiv").offsetLeft;
        },
        onSliderInput(v) {
            if(!this.preGreenBarValue) return
            console.log('onSliderInput0', v)
            if(this.preGreenBarValue[0] >= v[1]) {
                this.isReverse = !this.isReverse
                this.activeHandleIndex = 0
            } else if(this.preGreenBarValue[1] <= v[0]) {
                this.isReverse = !this.isReverse
                this.activeHandleIndex = 1
            }

            if(this.isReverse)
                [v[1], v[0]] = [...v]
            v[this.activeHandleIndex] = Math.round(+this.activeSlid.style.left.replace("%", '') / 100 * this.cycleDuration)
                + ((this.preGreenBarValue[this.activeHandleIndex] < v[this.activeHandleIndex]) ? 1 : -1)

            console.log('onSliderInput - activeHandleIndex', this.activeHandleIndex)
            v = this.activeHandleIndex ? [this.preGreenBarValue[0], v[1]] : [v[0], this.preGreenBarValue[1]]

            if(v[1] + this.yellowDuration + this.allRedDuration >= this.cycleDuration)
                v[1] = this.cycleDuration - this.yellowDuration - this.allRedDuration
            console.log('onSliderInput1',v)
            if(v[1] - v[0] > this.maxGreen) {
                if(this.preGreenBarValue[0] > v[0])
                    v[1] = v[0] + this.maxGreen
                else if(this.preGreenBarValue[1] < v[1])
                    v[0] = v[1] - this.maxGreen
            }

            console.log('onSliderInput2',v)
            this.greenBarValue = v
            this.preGreenBarValue = v
            this.$emit('slidden', v)
        },
        displaySignalTimingSlider(index) {
            this.getSignalTimingSliderPosition()
            if(this.activeHandleIndex < 0) this.activeHandleIndex = index;
            this.displayedAlignDashLine++
        },
        hideSignalTimingSlider() {
            if(this.displayedAlignDashLine)
                if(!--this.displayedAlignDashLine)
                    // window.setTimeout(() => this.activeHandleIndex = -1, 0)
                    this.activeHandleIndex = -1
        },
    },
    watch:{
        greenStartPoint(v) {
            this.greenStartPointProxy = v
        },
        greenDuration(v) {
            this.greenDurationProxy = v
        },
        greenEndPoint() {
            if(this.displayedAlignDashLine) this.displayedAlignDashLineIndex = 1
        },
        greenStartPointProxy(v) {
            if(this.displayedAlignDashLine) this.displayedAlignDashLineIndex = 0
            this.$emit('update:greenStartPoint', v)
        },
        greenDurationProxy(v) {
            this.$emit('update:greenDuration', v)
        },

        activeHandleIndex(v) {
            console.log("activeHandleIndex", v)
        }
    }
}
