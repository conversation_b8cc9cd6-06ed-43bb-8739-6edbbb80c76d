package com.lumlux.signal.service;

import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import java.util.*;

import com.lumlux.commons.entity.*;

import com.lumlux.signal.entity.*;
//import com.netsdk.dto.CameraParameterDto;
//import com.netsdk.dto.CameraParameterDto;
import com.lumlux.signal.util.NumberUtils;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.filterchain.IoFilterAdapter;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.core.service.IoConnector;
import org.apache.mina.core.service.IoHandler;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.transport.socket.nio.NioSocketConnector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Service;

@Service
public class DaemonService {

    private static final byte IFRAME_FLAG = 0x6b;

    private Logger logger = LoggerFactory.getLogger(getClass());

    static Object locker = new Object();

    static IoSession session = null;

    private int port;

//	private int port = 41117; //mj

    private String ip;
//	private String ip = "127.0.0.1"; //mj

    public void setPort(int port) {
        this.port = port;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    private void init() {
        synchronized (locker) {
            if (session == null) {
                try {
                    final IoConnector connector = new NioSocketConnector();

                    connector.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));

                    connector.getSessionConfig().setIdleTime(IdleStatus.BOTH_IDLE, 30000); // 读写都空闲时间:30秒
                    connector.getSessionConfig().setIdleTime(IdleStatus.READER_IDLE, 40000);// 读(接收通道)空闲时间:40秒
                    connector.getSessionConfig().setIdleTime(IdleStatus.WRITER_IDLE, 50000);// 写(发送通道)空闲时间:50秒

                    connector.setHandler(new DaemonClientHandler());

                    // 断线重连回调拦截器
                    connector.getFilterChain().addFirst("reconnection", new IoFilterAdapter() {
                        @Override
                        public void sessionClosed(NextFilter nextFilter, IoSession ioSession) throws Exception {
                            for (; ; ) {
                                try {
                                    Thread.sleep(3000);
                                    ConnectFuture future = connector.connect(new InetSocketAddress(ip, port));
                                    future.awaitUninterruptibly();// 等待连接创建成功
                                    session = future.getSession();// 获取会话
                                    if (session.isConnected()) {
                                        logger.debug("断线重连成功");
                                        break;
                                    }
                                } catch (Exception ex) {
                                    logger.error("重连服务器登录失败,3秒再连接一次:" + ex.getMessage());
                                }
                            }
                        }
                    });

                    ConnectFuture connectFuture = connector.connect(new InetSocketAddress(ip, port));
                    connectFuture.awaitUninterruptibly();
                    session = connectFuture.getSession();

                    /*
                     * new Timer("Timer").schedule(new java.util.TimerTask() {
                     *
                     * @Override public void run() {
                     * logger.debug("call Timer Task!"); ping(); } },15000);
                     */
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                    session = null;
                }
            }
        }
    }

    /****
     * 0x6b 1byte 11 byte 1byte <=768 byte 0x6b
     *
     * 0x1c ping 0x1d pong 0x10 1请求 0x11 1回复 //暂不写 0x12 2请求 0x13 2回复 //暂不写 0x14
     * 3请求 todo 0x15 3回复 //暂不写 0x16 4请求 todo 0x17 4回复 //暂不写 0x18 5请求 0x19 5回复
     * 机柜型 0x80
     * //暂不写 0x1a 6请求 0x1b 6回复 //暂不写
     */

    /*
     * private void ping() { byte[] data = new byte[] { 0x00, 0x00, 0x00, 0x00,
     * 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
     * data[0] = IFRAME_FLAG; data[1] = 0x01; data[14] = IFRAME_FLAG;
     * session.write(IoBuffer.wrap(data)); }
     */

    /**
     * 下发机柜型故障次数
     * @param strBaImei
     * @param failureWindowsSum
     */
    public void putConfigCabinet(String strBaImei, long failureWindowsSum) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[30];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0x57;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        if (failureWindowsSum >= Byte.MIN_VALUE && failureWindowsSum <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[13] = (byte) 0;
            data[14] = (byte) failureWindowsSum;
        } else {
            data[13] = (byte) (failureWindowsSum >> 8); // 高位字节
            data[14] = (byte) failureWindowsSum;        // 低位字节
        }
//        data[14] = (byte)failureWindowsSum;
        data[15] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

   /* *//**
     *    
     *
     * @param nChannelID
     * @param cameraParameterDto
     *//*
   public void getComePerson(String nChannelID, CameraParameterDto cameraParameterDto) {
        init();
        byte[] baImei = nChannelID.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x70;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)cameraParameterDto.getOnecome();
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }*/
    /**
     * 获取回路历史中功率和电压 发送故障时主动上报
     *
     * @param strBaImei
     * @param cardNo
     */
    public void getCircuitHistory(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x10;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 获取回路历史中功率和电压 发送故障时主动上报
     *
     * @param strBaImei
     * @param cardNo
     */
    public void getNewCircuitHistory(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x1C;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 获取基准功率
     *
     * @param strBaImei
     * @param cardNo
     */
    public void getPoiData(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x12;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 下放基准功率
     *
     * @param strBaImei
     * @param cardNo
     */
    public void putPoiData(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x14;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void resetLimit(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x60;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void putVariableConfig(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x24;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void getVariableConfig(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x26;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 配置启用位
     *
     * @param strBaImei
     * @param cardNo
     */
    public void configEnable(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x16;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 配置时间
     *
     * @param strBaImei
     * @param cardNo
     */
    public void configTime(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x18;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 读取时间
     *
     * @param strBaImei
     * @param cardNo
     */
    public void readTime(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x22;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 配置板卡数
     *
     * @param strBaImei
     * @param number
     */
    public void putBoardQuantity(String strBaImei, int number) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x1A;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) number;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 开关继电器
     *
     * @param strBaImei
     * @param
     * @throws InterruptedException
     */
    public void putControl(String strBaImei, Map<Integer, Integer> controlMap) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15 + ((int) controlMap.size()) * 2];
        data[0] = IFRAME_FLAG;
        data[1] = 0x1E;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) controlMap.size();
        int i = 1;
        for (Map.Entry<Integer, Integer> entry : controlMap.entrySet()) {
            data[13 + i] = (byte) Integer.parseInt(entry.getKey().toString());
            data[13 + i + 1] = (byte) Integer.parseInt(entry.getValue().toString());
            i += 2;
        }
        data[13 + i] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void getVersion(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x30;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 可变车道心跳注册
     * @param strBaImei
     * @param configParam
     * @throws InterruptedException
     */
    public void putConfig1(String strBaImei, ConfigParam configParam) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[165];
        data[0] = IFRAME_FLAG;
        data[1] = 0x32;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[14] = (byte) configParam.getStateInterval().intValue();//心跳包间隔：
        data[15] = (byte) configParam.getStatusOfflineInterval().intValue(); // 数据包传输间隔
        data[16] = (byte) configParam.getStatusPartInvalidInterval().intValue(); // 系统重启
        data[17] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 下发调光配置
     * @param strBaImei
     * @param dimmingParam
     * @throws InterruptedException
     */
    public void putDimmingParamConfig(String strBaImei, DimmingParam dimmingParam) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[37];
        data[0] = IFRAME_FLAG;
        data[1] = 0x62;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[14] = (byte) Integer.parseInt(dimmingParam.getStartTime().split(":")[0]);
        data[15] = (byte) Integer.parseInt(dimmingParam.getStartTime().split(":")[1]);
        data[16] = (byte) Integer.parseInt(dimmingParam.getStartTime().split(":")[2]);
        data[17] = (byte) Integer.parseInt(dimmingParam.getEndTime().split(":")[0]);
        data[18] = (byte) Integer.parseInt(dimmingParam.getEndTime().split(":")[1]);
        data[19] = (byte) Integer.parseInt(dimmingParam.getEndTime().split(":")[2]);
//		data[13] = (byte) cardNo;
        data[20] = (byte) dimmingParam.getControlStatus().intValue();
//        data[21] = 0x62;
        data[22] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }
    /**
     * 下发调光配置
     * @param strBaImei
     * @param dimmingParam
     * @throws InterruptedException
     */
    public void putDimmingPortableConfig(String strBaImei,int count, DimmingParam dimmingParam) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[37];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xb6;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[14] = (byte)count;
        data[15] = (byte) Integer.parseInt(dimmingParam.getStartTime().split(":")[0]);
        data[16] = (byte) Integer.parseInt(dimmingParam.getStartTime().split(":")[1]);
        data[17] = (byte) Integer.parseInt(dimmingParam.getStartTime().split(":")[2]);
        data[18] = (byte) Integer.parseInt(dimmingParam.getEndTime().split(":")[0]);
        data[19] = (byte) Integer.parseInt(dimmingParam.getEndTime().split(":")[1]);
        data[20] = (byte) Integer.parseInt(dimmingParam.getEndTime().split(":")[2]);
//		data[13] = (byte) cardNo;
        data[21] = (byte) dimmingParam.getControlStatus().intValue();
//        data[21] = 0x62;
        data[22] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 下发黄灯单元配置
     * @param strBaImei
     * @param lightBean
     */
    public void putYellowLightConfig(String strBaImei, YellowLightBean lightBean) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[20];
        data[0] = IFRAME_FLAG;
        data[1] = 0x66;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) lightBean.getControlSwitch();
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 下放通道信息配置(有问题)
     * @param strBaImei
     * @param routerBean
     * @throws InterruptedException
     */
    public void putConfig3(String strBaImei, RouterBean routerBean) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[100];
        data[0] = IFRAME_FLAG;
        data[1] = 0x64;
        int k = 0,j=0;
        int sum=0;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        if (null != routerBean) {
            int controlType = routerBean.getControlType();
            List<RouterParam> routerParams = routerBean.getRouterParam();
                if (routerParams.size() > 0) {  //3方案
                    for ( j= 0 ; j < routerParams.size(); j++) {
                        if (routerParams.get(j)!= null && routerParams.get(j).getEnable()!=null) {
                            List<Integer> enables = routerParams.get(j).getEnable();
                            if (enables.size() > 0) {
                                data[12+1+j+sum] = (byte) controlType;//星期
//                                System.out.println(12+1+j+sum);
                                data[12+2+j+sum] = (byte) (j);//方案
//                               System.out.println(12+2+j+sum);
                                data[12+3+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[0]);
//                                System.out.println(12+3+j+sum);
                                data[12+4+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[1]);
//                                System.out.println(12+4+j+sum);
                                data[12+5+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[2]);
//                                System.out.println(12+5+j+sum);
                                data[12+6+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[0]);
//                                System.out.println(12+6+j+sum);
                                data[12+7+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[1]);
//                                System.out.println(12+7+j+sum);
                                data[12+8+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[2]);
//                                System.out.println(12+8+j+sum);

                                for (k=0 ; k < enables.size(); k++) {
                                    data[12+8+j+k+1+sum] = (byte) enables.get(k).intValue();
//                                    System.out.println(12+8+j+k+1+sum);
                                }
                                sum= sum+k+7;
//                                System.out.println(k);
                            }
                        }
                    }
                    // 計算
//                    data[85] = 0x64;
                    data[86] = IFRAME_FLAG;
                    if (session != null) {
                        session.write(IoBuffer.wrap(data));
                    }
                }
        }
    }


    public void putConfigTest(String code, RouterBean routerBean) throws InterruptedException {
        init();
        byte[] baImei = code.getBytes();
        byte[] data = new byte[100];
        data[0] = IFRAME_FLAG;
        data[1] = 0x1d;
        int k = 0;

        System.arraycopy(baImei, 0, data, 2, baImei.length);
        if (null != routerBean) {
            int controlType = routerBean.getControlType();
            List<RouterParam> routerParams = routerBean.getRouterParam();
            if (routerParams.size() > 0) {  //3方案
                        List<Integer> enables = routerParams.get(0).getEnable();
                            data[12+1] = (byte) controlType;//星期
//                                System.out.println(12+1+j+sum);
                            data[12+2] = (byte) (0);//方案
//                               System.out.println(12+2+j+sum);
                            data[12+3] = (byte) Integer.parseInt(routerParams.get(0).getStartTime().split(":")[0]);
//                                System.out.println(12+3+j+sum);
                            data[12+4] = (byte) Integer.parseInt(routerParams.get(0).getStartTime().split(":")[1]);
//                                System.out.println(12+4+j+sum);
                            data[12+5] = (byte) Integer.parseInt(routerParams.get(0).getStartTime().split(":")[2]);
//                                System.out.println(12+5+j+sum);
                            data[12+6] = (byte) Integer.parseInt(routerParams.get(0).getEndTime().split(":")[0]);
//                                System.out.println(12+6+j+sum);
                            data[12+7] = (byte) Integer.parseInt(routerParams.get(0).getEndTime().split(":")[1]);
//                                System.out.println(12+7+j+sum);
                            data[12+8] = (byte) Integer.parseInt(routerParams.get(0).getEndTime().split(":")[2]);
//                                System.out.println(12+8+j+sum);
                        if (enables.size() > 0) {
                            for (k=0 ; k < enables.size(); k++) {
                                data[12+8+k+1] = (byte) enables.get(k).intValue();
//                                    System.out.println(12+8+j+k+1+sum);
                            }
                    }
                data[86] = IFRAME_FLAG;
                if (session != null) {
                    session.write(IoBuffer.wrap(data));
                }
            }
        }
    }

    public void putConfigTestTwo(String strBaImei, RouterBean routerBean) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[62];
        data[0] = IFRAME_FLAG;
        data[1] = 0x1d;
        int k = 0,j=0;
        int sum=0;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        if (null != routerBean) {
            int controlType = routerBean.getControlType();
            List<RouterParam> routerParams = routerBean.getRouterParam();
            if (routerParams.size() > 0) {  //3方案
                for ( j= 0 ; j < routerParams.size(); j++) {
                    if (routerParams.get(j)!= null && routerParams.get(j).getEnable()!=null) {
                        List<Integer> enables = routerParams.get(j).getEnable();
                        if (enables.size() > 0) {
                            data[12+1+j+sum] = (byte) controlType;//星期
//                                System.out.println(12+1+j+sum);
                            data[12+2+j+sum] = (byte) (j);//方案
//                               System.out.println(12+2+j+sum);
                            data[12+3+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[0]);
//                                System.out.println(12+3+j+sum);
                            data[12+4+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[1]);
//                                System.out.println(12+4+j+sum);
                            data[12+5+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[2]);
//                                System.out.println(12+5+j+sum);
                            data[12+6+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[0]);
//                                System.out.println(12+6+j+sum);
                            data[12+7+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[1]);
//                                System.out.println(12+7+j+sum);
                            data[12+8+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[2]);
//                                System.out.println(12+8+j+sum);

                            for (k=0 ; k < enables.size(); k++) {
                                data[12+8+j+k+1+sum] = (byte) enables.get(k).intValue();
//
                            }
                            sum= sum+k+7;
//                                System.out.println(k);
                        }
                    }
                }
                data[61] = IFRAME_FLAG;
                if (session != null) {
                    session.write(IoBuffer.wrap(data));
                }
            }
        }
    }

    public void putConfigTraversalVariable(String strBaImei, int controlType,int scheme,RouterParam routerParam) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[40];
        data[0] = IFRAME_FLAG;
        data[1] = 0x1d;

        int sum=0;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        List<Integer> enables = routerParam.getEnable();
        data[12+1] = (byte) controlType;//星期
//                                System.out.println(12+1+j+sum);
        data[12+2] = (byte) scheme;//方案
        data[12+3] = (byte) Integer.parseInt(routerParam.getStartTime().split(":")[0]);
        data[12+4] = (byte) Integer.parseInt(routerParam.getStartTime().split(":")[1]);
        data[12+5] = (byte) Integer.parseInt(routerParam.getStartTime().split(":")[2]);
        data[12+6] = (byte) Integer.parseInt(routerParam.getEndTime().split(":")[0]);
        data[12+7] = (byte) Integer.parseInt(routerParam.getEndTime().split(":")[1]);
        data[12+8] = (byte) Integer.parseInt(routerParam.getEndTime().split(":")[2]);
        for (int k=0 ; k < enables.size(); k++) {
            data[12+8+k+1] = (byte) enables.get(k).intValue();
//
        }
        data[37] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }

    }

    public void putConfigTestThree(String strBaImei, RouterBean routerBean) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[86];
        data[0] = IFRAME_FLAG;
        data[1] = 0x1d;
        int k = 0,j=0;
        int sum=0;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        if (null != routerBean) {
            int controlType = routerBean.getControlType();
            List<RouterParam> routerParams = routerBean.getRouterParam();
            if (routerParams.size() > 0) {  //3方案
                for ( j= 0 ; j < routerParams.size(); j++) {
                    if (routerParams.get(j)!= null && routerParams.get(j).getEnable()!=null) {
                        List<Integer> enables = routerParams.get(j).getEnable();
                        if (enables.size() > 0) {
                            data[12+1+j+sum] = (byte) controlType;//星期
//                                System.out.println(12+1+j+sum);
                            data[12+2+j+sum] = (byte) (j);//方案
//                               System.out.println(12+2+j+sum);
                            data[12+3+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[0]);
//                                System.out.println(12+3+j+sum);
                            data[12+4+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[1]);
//                                System.out.println(12+4+j+sum);
                            data[12+5+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getStartTime().split(":")[2]);
//                                System.out.println(12+5+j+sum);
                            data[12+6+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[0]);
//                                System.out.println(12+6+j+sum);
                            data[12+7+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[1]);
//                                System.out.println(12+7+j+sum);
                            data[12+8+j+sum] = (byte) Integer.parseInt(routerParams.get(j).getEndTime().split(":")[2]);
//                                System.out.println(12+8+j+sum);

                            for (k=0 ; k < enables.size(); k++) {
                                data[12+8+j+k+1+sum] = (byte) enables.get(k).intValue();
//
                            }
                            sum= sum+k+7;
//                                System.out.println(k);
                        }
                    }
                }
                data[85] = IFRAME_FLAG;
                if (session != null) {
                    session.write(IoBuffer.wrap(data));
                }
            }
        }
    }

    public void putConfig(String strBaImei, List<PutConfig> list) throws InterruptedException {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[165];
        data[0] = IFRAME_FLAG;
        data[1] = 0x32;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        int i = 1;
        for (PutConfig config : list) {
            // 数据
            if (config.getKey() == 0x22 || config.getKey() == 0x32 || config.getKey() == 0x42 || config.getKey() == 0x52) {
                String str = config.getValue();
                data[13 + i] = config.getKey();
                data[13 + i + 1] = 0x06;
                data[13 + i + 2] = (byte) Integer.parseInt(str.split(",")[0]);
                data[13 + i + 3] = (byte) Integer.parseInt(str.split(",")[1]);
                data[13 + i + 4] = (byte) Integer.parseInt(str.split(",")[2]);
                data[13 + i + 5] = (byte) Integer.parseInt(str.split(",")[3]);
                data[13 + i + 6] = (byte) Integer.parseInt(str.split(",")[4]);
                data[13 + i + 7] = (byte) Integer.parseInt(str.split(",")[5]);
                i += 8;
            } else if (config.getKey() == 0x01 || config.getKey() == 0x02 || config.getKey() == 0x20 || config.getKey() == 0x30 || config.getKey() == 0x40 || config.getKey() == 0x50) {
                data[13 + i] = config.getKey();
                data[13 + i + 1] = 0x01;
                data[13 + i + 2] = (byte) Integer.parseInt(config.getValue().toString());
                i += 3;
            } else {
                data[13 + i] = config.getKey();
                data[13 + i + 1] = 0x02;
                data[13 + i + 2] = (byte) (Integer.parseInt((config.getValue()).toString()) >> 8);
                data[13 + i + 3] = (byte) Integer.parseInt(config.getValue().toString());
                i += 4;
            }
        }
        data[13] = (byte) (i - 1);
        data[13 + i] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }


    public void getConfig(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x34;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void putTimeControl(String strBaImei, List<SolutionSettings> solutionList) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15 + (solutionList.size() * 8)];
        data[0] = IFRAME_FLAG;
        data[1] = 0x40;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        int i = 1;
        int size = 0;
        for (SolutionSettings solution : solutionList) {
            if ((!solution.getValue1().isEmpty()) && (!solution.getValue2().isEmpty()) && (!solution.getValue3().isEmpty()) && (!solution.getValue4().isEmpty())) {
                data[13 + i] = 1;
                data[13 + i + 1] = (byte) Integer.parseInt(solution.getEnabled1());
                data[13 + i + 2] = 2;
                data[13 + i + 3] = (byte) Integer.parseInt(solution.getEnabled2());
                data[13 + i + 4] = (byte) Integer.parseInt(solution.getValue1());
                data[13 + i + 5] = (byte) Integer.parseInt(solution.getValue2());
                data[13 + i + 6] = (byte) Integer.parseInt(solution.getValue3());
                data[13 + i + 7] = (byte) Integer.parseInt(solution.getValue4());
                i += 8;
                size++;
            }
        }
        data[13] = (byte) size;
        data[13 + i] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void putTimeControlEnabled(String strBaImei, int cardNo) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x44;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) cardNo;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void inform(String strBaImei, String strPhone, String strId) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] phone = strPhone.getBytes();
        byte[] id = strId.getBytes();
        byte[] data = new byte[57];
        data[0] = IFRAME_FLAG;
        data[1] = 0x20;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        System.arraycopy(phone, 0, data, 13, phone.length);
        System.arraycopy(id, 0, data, 24, id.length);
        data[56] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void secondConfigEnable(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x5e;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void secondPutPoiData(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x54;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void secondGetReference(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x5c;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void secondPutParameter(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x58;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void secondPutDeviceRestart(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x59;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void secondPutAlarmConfig(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = (byte) 0xC5;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }
    public void secondPutRed(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = (byte) 0xC6;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }
    public void secondPutGreen(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = (byte) 0xC7;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }
    public void secondPutHorn(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = (byte) 0xC8;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }
    public void secondPutDisplay(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = (byte) 0xC9;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void secondGetParameter(String strBaImei) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = 0x5a;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) 0xff;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * 下放计划，
     * 1. 下发计划
     * 2.都扩大了10倍
     * @param strBaImei
     * @param lightValueBean
     */

    public void putAdHOCShemeConfig(String strBaImei, LightValueBean lightValueBean,String model) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[38];
        data[0] = IFRAME_FLAG;
            if ("15".equals(model)) {
                data[1] = (byte)0xb1;
            } else {
                data[1] = (byte)0xb4;
            }

        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13]= (byte) lightValueBean.getCount();//包序列号
        if ("15".equals(model)) {
            data[14]= (byte) 0x02;//包类型：02表示配置信控方案
        } else {
            data[14]= (byte) 0x01;
        }
        data[15]= (byte) lightValueBean.getLightTotal();//总灯数
//        data[16]= (byte) Integer.parseInt(lightValueBean.getLightNumber());//本灯号

        data[16] = NumberUtils.mergeNibbles(lightValueBean.getLightStatus(), Integer.parseInt(lightValueBean.getLightNumber()));

        data[17]= (byte) Integer.parseInt(lightValueBean.getSchemeNo());//方案号
        byte cycleTotalLength = (byte) ((Integer.parseInt(lightValueBean.getCycleTotalLength())));//本方案信控总时长
        if (cycleTotalLength > 0 && cycleTotalLength <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[18] = (byte) 0;
            data[19] =  cycleTotalLength;
        } else {
            data[18] = (byte) ((cycleTotalLength >> 8) & 0xFF); // 高位字节
            data[19] =  cycleTotalLength ;        // 低位字节
        }
        byte frequentGreen = (byte) ((Integer.parseInt(lightValueBean.getFrequentGreen())));//绿灯时长
        if (frequentGreen >= 0 && frequentGreen <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[20] = (byte) 0;
            data[21] =  frequentGreen;
        } else {
            data[20] = (byte) ((frequentGreen >> 8) & 0xFF); // 高位字节
            data[21] =  frequentGreen;        // 低位字节
        }
        byte yellowLight = (byte) ((Integer.parseInt(lightValueBean.getYellowLight())));//黄灯时长
        if (yellowLight > 0 && yellowLight <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[22] = (byte) 0;
            data[23] =  yellowLight;
        } else {
            data[22] = (byte) ((yellowLight >> 8 ) & 0xFF); // 高位字节
            data[23] =  yellowLight;        // 低位字节
        }
        byte redTime = (byte) ((Integer.parseInt(lightValueBean.getRedTime())));////红计时
//        byte redTime = (byte) Double.parseDouble(lightValueBean.getRedTime());//红计时
        if (redTime > 0 && redTime <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[24] = (byte) 0;
            data[25] =  redTime;
        } else {
            data[24] = (byte) ((redTime >> 8) & 0xFF); // 高位字节
            data[25] =  redTime;        // 低位字节
        }
        byte safeGreen = (byte) ((Integer.parseInt(lightValueBean.getSafeGreen())));////安全绿时
//        byte safeGreen = (byte) Double.parseDouble(lightValueBean.getSafeGreen());//安全绿时
        if (safeGreen > 0 && safeGreen <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[26] = (byte) 0;
            data[27] =  safeGreen;
        } else {
            data[26] = (byte) ((safeGreen >> 8) & 0xFF); // 高位字节
            data[27] =  safeGreen;        // 低位字节
        }
        byte wholeRed = (byte) ((Integer.parseInt(lightValueBean.getWholeRed())));//全红
//        byte wholeRed = (byte) Double.parseDouble(lightValueBean.getWholeRed());//全红
        if (wholeRed > 0 && wholeRed <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[28] = (byte) 0;
            data[29] =  wholeRed;
        } else {
            data[28] = (byte) ((wholeRed >> 8) & 0xFF); // 高位字节
            data[29] =  wholeRed;        // 低位字节
        }
        byte greenTime = (byte) ((Integer.parseInt(lightValueBean.getGreenTime())));//绿计时
//        byte greenTime = (byte) Double.parseDouble(lightValueBean.getGreenTime());//绿计时
        if (greenTime > 0 && greenTime <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[30] = (byte) 0;
            data[31] =  greenTime;
        } else {
            data[30] = (byte) ((greenTime >> 8) & 0xFF); // 高位字节
            data[31] =  greenTime;        // 低位字节
        }
        byte motoVehiGreenFlash = (byte) ((Integer.parseInt(lightValueBean.getMotoVehiGreenFlash())));//机动绿闪
//        byte motoVehiGreenFlash = (byte) Double.parseDouble(lightValueBean.getMotoVehiGreenFlash());//机动绿闪
        if (motoVehiGreenFlash > 0 && motoVehiGreenFlash <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[32] = (byte) 0;
            data[33] =  motoVehiGreenFlash;
        } else {
            data[32] = (byte) ((motoVehiGreenFlash >> 8) & 0xFF); // 高位字节
            data[33] =  motoVehiGreenFlash;        // 低位字节
        }
        byte greenStartTime = (byte) ((Integer.parseInt(lightValueBean.getGreenStartTime())));//绿灯开始时间
//        byte greenStartTime = (byte) Double.parseDouble(lightValueBean.getGreenStartTime());//绿灯开始时间
        if (greenStartTime > 0 && greenStartTime <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[34] = (byte) 0;
            data[35] =  greenStartTime;
        } else {
            data[34] = (byte) ((greenStartTime >> 8) & 0xFF); // 高位字节
            data[35] =  (byte)(greenStartTime  & 0xFF);        // 低位字节
        }
        data[36] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void putTimePlanInfo(String strBaImei,int count,PutTimePlanBean timePlanBean,String model) {
        init();
        byte[] baImei = strBaImei.getBytes();
//        byte[] data = new byte[25];//
        byte[] data = new byte[38];//
        data[0] = IFRAME_FLAG;


        if ("15".equals(model)) {
            data[1] = (byte)0xb2;//自组网
        } else {
            data[1] = (byte)0xb9;//移动信号灯
        }
//        data[1] = (byte)0xb2;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13]= (byte) count;//包序列号
//        if (model)
            if ("15".equals(model)) {
                data[14]= (byte) 0x02;//包类型：02表示自组网计划配置
            } else {
                data[14]= (byte) 0x01;//包类型：01表示移动信号灯计划配置
            }
//        data[14]= (byte) 0x02;//包类型：02表示计划配置
        data[15]= (byte) Integer.parseInt(timePlanBean.getControlType());//星期
//        data[16]= (byte) Integer.parseInt(timePlanBean.getPlanNo());//计划号
        int sum=0;
        List<PutTimePlanDetailBean> planDetailList = timePlanBean.getPlanDetailList();
        if (planDetailList != null && planDetailList.size() > 0) {
            for (int i = 0; i < planDetailList.size(); i++) {
                data[16+i+sum] = (byte) Integer.parseInt(planDetailList.get(i).getSchemeStartTime().split(":")[0]);
//                                System.out.println(12+3+j+sum);
                data[17+i+sum] = (byte) Integer.parseInt(planDetailList.get(i).getSchemeStartTime().split(":")[1]);
//                                System.out.println(12+4+j+sum);
                data[18+i+sum] = (byte) Integer.parseInt(planDetailList.get(i).getSchemeStartTime().split(":")[2]);
                data[19+i+sum] = (byte) Integer.parseInt(planDetailList.get(i).getSchemeEndTime().split(":")[0]);

                data[20+i+sum] = (byte) Integer.parseInt(planDetailList.get(i).getSchemeEndTime().split(":")[1]);

                data[21+i+sum] = (byte) Integer.parseInt(planDetailList.get(i).getSchemeEndTime().split(":")[2]);
                data[22+i+sum]  = (byte) Integer.parseInt(planDetailList.get(i).getSchemeNo());
                sum=sum+6;
            }
        }
        /*data[17]= (byte) Integer.parseInt(timePlanBean.getSchemeNo());//计划号
        data[18]= (byte) Integer.parseInt(timePlanBean.getSchemeStartTime().split(":")[0]);
        data[19]= (byte) Integer.parseInt(timePlanBean.getSchemeStartTime().split(":")[1]);
        data[20]= (byte) Integer.parseInt(timePlanBean.getSchemeStartTime().split(":")[2]);
        data[21] = (byte)Integer.parseInt(timePlanBean.getSchemeEndTime().split(":")[0]);
        data[22] = (byte)Integer.parseInt(timePlanBean.getSchemeEndTime().split(":")[1]);
        data[23] = (byte)Integer.parseInt(timePlanBean.getSchemeEndTime().split(":")[2]);*/
        /* int sum =0;
       if (timePlanDetailBeanList.size() > 0 || timePlanDetailBeanList !=null) {
            for (int j = 0; j < timePlanDetailBeanList.size(); j++){

                data[16+j+sum] = (byte) Integer.parseInt(timePlanDetailBeanList.get(j).getSchemeStartTime().split(":")[0]);
//                                System.out.println(12+3+j+sum);
                data[17+j+sum] = (byte) Integer.parseInt(timePlanDetailBeanList.get(j).getSchemeStartTime().split(":")[1]);
//                                System.out.println(12+4+j+sum);
                data[18+j+sum] = (byte) Integer.parseInt(timePlanDetailBeanList.get(j).getSchemeStartTime().split(":")[2]);
                data[19+j+sum] = (byte) Integer.parseInt(timePlanDetailBeanList.get(j).getSchemeEndTime().split(":")[0]);

                data[20+j+sum] = (byte) Integer.parseInt(timePlanDetailBeanList.get(j).getSchemeEndTime().split(":")[1]);

                data[21+j+sum] = (byte) Integer.parseInt(timePlanDetailBeanList.get(j).getSchemeEndTime().split(":")[2]);
                data[22+j+sum]  = (byte) Integer.parseInt(timePlanDetailBeanList.get(j).getSchemeNo());
                sum=sum+6;
            }
        }*/
        data[37] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * a1
     * @param strBaImei
     * @param putAdHOCHeartBeatBean
     */
    public void putAdHOCHeartBeat(String strBaImei, PutAdHOCHeartBeatBean putAdHOCHeartBeatBean) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[20];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xa1;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        int heartBeatInterval = Integer.parseInt(putAdHOCHeartBeatBean.getHeartBeatInterval());
        if (heartBeatInterval >= 0 && heartBeatInterval <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[13] = (byte) 0;
            data[14] = (byte) heartBeatInterval;
        } else {
            data[13] = (byte) (heartBeatInterval >> 8); // 高位字节
            data[14] = (byte) heartBeatInterval;        // 低位字节
        }
        int dataInterval = Integer.parseInt(putAdHOCHeartBeatBean.getDataInterval());
        if (dataInterval >= 0 && dataInterval <= Byte.MAX_VALUE) {
        //value两个byte表示
        data[15] = (byte) 0;
        data[16] = (byte) dataInterval;
        } else {
        data[15] = (byte) (dataInterval >> 8); // 高位字节
        data[16] = (byte) dataInterval;        // 低位字节
        }

        int invalidInterval = Integer.parseInt(putAdHOCHeartBeatBean.getStatusPartInvalidInterval());
        if (invalidInterval >= 0 && invalidInterval <= Byte.MAX_VALUE) {
            //value两个byte表示
            data[17] = (byte) 0;
            data[18] = (byte) invalidInterval;
        } else {
            data[17] = (byte) (invalidInterval >> 8); // 高位字节
            data[18] = (byte) invalidInterval;        // 低位字节
        }

        data[19] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }

    }

    /**
     * a2
     * @param strBaImei
     * @param putAdHOCQueryInfoBean
     */
    public void putAdHOCQueryInfo(String strBaImei, int count,PutAdHOCQueryInfoBean putAdHOCQueryInfoBean) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[16];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xa2;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        data[14] = (byte) Integer.parseInt(putAdHOCQueryInfoBean.getAdHOCQType());
        data[15] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }
    /**
     * ac
     * @param strBaImei
     * @param putAdHOCQueryInfoBean
     */
    public void putQueryAdHOCInfo(String strBaImei, int count,PutAdHOCQueryInfoBean putAdHOCQueryInfoBean) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[16];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xac;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        data[14] = (byte) Integer.parseInt(putAdHOCQueryInfoBean.getAdHOCQType());
        data[15] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }




    /**
     * a4
     * @param strBaImei
     * @param count
     * @param
     */
    public void putAdHOCFollowControllerInfo(String strBaImei, int count, List followLightAdressBytesList,String status,String model) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[25];
        data[0] = IFRAME_FLAG;
        if ("15".equals(model)) {
            data[1] = (byte)0xa4;
        } else {
            data[1] = (byte)0xaa;
        }
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        if ("15".equals(model)) {
            data[14] = 0x03;
        }else {
            data[14] = 0x01;
        }
            data[15] = (byte) Integer.parseInt(followLightAdressBytesList.get(0).toString());
            data[16] = (byte) Integer.parseInt(followLightAdressBytesList.get(1).toString());
            data[17] = (byte) Integer.parseInt(followLightAdressBytesList.get(2).toString());
            data[18] = (byte) Integer.parseInt(followLightAdressBytesList.get(3).toString());
            data[19] = (byte) Integer.parseInt(followLightAdressBytesList.get(4).toString());
            data[20] = (byte) Integer.parseInt(followLightAdressBytesList.get(5).toString());
            data[21] = (byte) Integer.parseInt(followLightAdressBytesList.get(6).toString());
            data[22] = (byte) Integer.parseInt(followLightAdressBytesList.get(7).toString());
            data[23] = (byte) Integer.parseInt(status);
            data[24] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * a5
     * @param strBaImei
     * @param
     */
    public void putMacInfo(String strBaImei, int count,String lightNumber,String lightType,List followLightAdressBytesList,List masterLightIdAdressBytesList,String model) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[36];
        data[0] = IFRAME_FLAG;

        if ("15".equals(model)) {
            data[1] = (byte)0xa5;
        } else {
            data[1] = (byte)0xab;
        }
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        if ("15".equals(model)) {
            data[14] = 0x04;
        } else {
            data[14] = 0x02;
        }
        data[15] = (byte) Integer.parseInt(followLightAdressBytesList.get(0).toString());
        data[16] = (byte) Integer.parseInt(followLightAdressBytesList.get(1).toString());
        data[17] = (byte) Integer.parseInt(followLightAdressBytesList.get(2).toString());
        data[18] = (byte) Integer.parseInt(followLightAdressBytesList.get(3).toString());
        data[19] = (byte) Integer.parseInt(followLightAdressBytesList.get(4).toString());
        data[20] = (byte) Integer.parseInt(followLightAdressBytesList.get(5).toString());
        data[21] = (byte) Integer.parseInt(followLightAdressBytesList.get(6).toString());
        data[22] = (byte) Integer.parseInt(followLightAdressBytesList.get(7).toString());
        data[23] = (byte)Integer.parseInt(lightType);
        data[24] = (byte)Integer.parseInt(lightNumber);
        data[25] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(0).toString());
        data[26] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(1).toString());
        data[27] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(2).toString());
        data[28] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(3).toString());
        data[29] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(4).toString());
        data[30] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(5).toString());
        data[31] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(6).toString());
        data[32] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(7).toString());
        data[33] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(8).toString());
        data[34] = (byte) Integer.parseInt(masterLightIdAdressBytesList.get(9).toString());
        data[35] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }

    }
    /**
     * a6
     * @param strBaImei
     * @param count
     * @param
     * @param
     */
    public void putDeleteAdHOCLightInfo(String strBaImei, int count, List followLightAdressBytesList,List masterLightAdressBytesList) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[34];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xa6;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        data[14] = 0x05;
        data[15] = (byte) Integer.parseInt(followLightAdressBytesList.get(0).toString());
        data[16] = (byte) Integer.parseInt(followLightAdressBytesList.get(1).toString());
        data[17] = (byte) Integer.parseInt(followLightAdressBytesList.get(2).toString());
        data[18] = (byte) Integer.parseInt(followLightAdressBytesList.get(3).toString());
        data[19] = (byte) Integer.parseInt(followLightAdressBytesList.get(4).toString());
        data[20] = (byte) Integer.parseInt(followLightAdressBytesList.get(5).toString());
        data[21] = (byte) Integer.parseInt(followLightAdressBytesList.get(6).toString());
        data[22] = (byte) Integer.parseInt(followLightAdressBytesList.get(7).toString());

        data[23] = (byte) Integer.parseInt(masterLightAdressBytesList.get(0).toString());
        data[24] = (byte) Integer.parseInt(masterLightAdressBytesList.get(1).toString());
        data[25] = (byte) Integer.parseInt(masterLightAdressBytesList.get(2).toString());
        data[26] = (byte) Integer.parseInt(masterLightAdressBytesList.get(3).toString());
        data[27] = (byte) Integer.parseInt(masterLightAdressBytesList.get(4).toString());
        data[28] = (byte) Integer.parseInt(masterLightAdressBytesList.get(5).toString());
        data[29] = (byte) Integer.parseInt(masterLightAdressBytesList.get(6).toString());
        data[30] = (byte) Integer.parseInt(masterLightAdressBytesList.get(7).toString());
        data[31] = (byte) Integer.parseInt(masterLightAdressBytesList.get(8).toString());
        data[32] = (byte) Integer.parseInt(masterLightAdressBytesList.get(9).toString());

        data[33] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * a7
     * @param strBaImei
     * @param
     */
    public void putAdHOCEndNetworkInfo(String strBaImei, int count, List masterLightAdressBytesList) {
        init();
        byte[] baImei = strBaImei.getBytes();
//        byte[] data = new byte[16];
        byte[] data = new byte[26];
        data[0] = IFRAME_FLAG;
//        data[1] = (byte)0x7a;
        data[1] = (byte)0xa7;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        data[14] = 0x06;
//        String[] split = strBaImei.split("");
        data[15] = (byte) Integer.parseInt(masterLightAdressBytesList.get(0).toString());
        data[16] = (byte) Integer.parseInt(masterLightAdressBytesList.get(1).toString());
        data[17] = (byte) Integer.parseInt(masterLightAdressBytesList.get(2).toString());
        data[18] = (byte) Integer.parseInt(masterLightAdressBytesList.get(3).toString());
        data[19] = (byte) Integer.parseInt(masterLightAdressBytesList.get(4).toString());
        data[20] = (byte) Integer.parseInt(masterLightAdressBytesList.get(5).toString());
        data[21] = (byte) Integer.parseInt(masterLightAdressBytesList.get(6).toString());
        data[22] = (byte) Integer.parseInt(masterLightAdressBytesList.get(7).toString());
        data[23] = (byte) Integer.parseInt(masterLightAdressBytesList.get(8).toString());
        data[24] = (byte) Integer.parseInt(masterLightAdressBytesList.get(9).toString());
        data[25] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }



    /**
     * 7a
     * @param strBaImei
     * @param
     */
    public void putAdHOCStartNetworkInfo(String strBaImei, int count, List masterLightAdressBytesList) {
        init();
        byte[] baImei = strBaImei.getBytes();
//        byte[] data = new byte[16];
        byte[] data = new byte[26];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0x7a;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        data[14] = 0x06;
//        String[] split = strBaImei.split("");
        data[15] = (byte) Integer.parseInt(masterLightAdressBytesList.get(0).toString());
        data[16] = (byte) Integer.parseInt(masterLightAdressBytesList.get(1).toString());
        data[17] = (byte) Integer.parseInt(masterLightAdressBytesList.get(2).toString());
        data[18] = (byte) Integer.parseInt(masterLightAdressBytesList.get(3).toString());
        data[19] = (byte) Integer.parseInt(masterLightAdressBytesList.get(4).toString());
        data[20] = (byte) Integer.parseInt(masterLightAdressBytesList.get(5).toString());
        data[21] = (byte) Integer.parseInt(masterLightAdressBytesList.get(6).toString());
        data[22] = (byte) Integer.parseInt(masterLightAdressBytesList.get(7).toString());
        data[23] = (byte) Integer.parseInt(masterLightAdressBytesList.get(8).toString());
        data[24] = (byte) Integer.parseInt(masterLightAdressBytesList.get(9).toString());
        data[25] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    /**
     * a8
     * @param strBaImei
     * @param count
     * @param
     */
    public void putAdHOCEndScannerInfo(String strBaImei, int count,List masterLightAdressBytesList) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[26];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xa8;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count;
        data[14] = 0x07;
        data[15] = (byte) Integer.parseInt(masterLightAdressBytesList.get(0).toString());
        data[16] = (byte) Integer.parseInt(masterLightAdressBytesList.get(1).toString());
        data[17] = (byte) Integer.parseInt(masterLightAdressBytesList.get(2).toString());
        data[18] = (byte) Integer.parseInt(masterLightAdressBytesList.get(3).toString());
        data[19] = (byte) Integer.parseInt(masterLightAdressBytesList.get(4).toString());
        data[20] = (byte) Integer.parseInt(masterLightAdressBytesList.get(5).toString());
        data[21] = (byte) Integer.parseInt(masterLightAdressBytesList.get(6).toString());
        data[22] = (byte) Integer.parseInt(masterLightAdressBytesList.get(7).toString());
        data[23] = (byte) Integer.parseInt(masterLightAdressBytesList.get(8).toString());
        data[24] = (byte) Integer.parseInt(masterLightAdressBytesList.get(9).toString());
        data[25] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void putMagneticLockInfo(String strBaImei, Integer magneticLockGoal) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0x69;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)magneticLockGoal.intValue();
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }

    }

    public void putPortableSchemeLevelInfo(String strBaImei, String schemeNo,Integer count) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[16];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xb5;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte)count.intValue();
        data[14] = (byte)Integer.parseInt(schemeNo);
        data[15] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }
    }

    public void putOnClickConfig(String strBaImei, int onclickParameter) {
        init();
        byte[] baImei = strBaImei.getBytes();
        byte[] data = new byte[15];
        data[0] = IFRAME_FLAG;
        data[1] = (byte)0xb8;
        System.arraycopy(baImei, 0, data, 2, baImei.length);
        data[13] = (byte) onclickParameter;
        data[14] = IFRAME_FLAG;
        if (session != null) {
            session.write(IoBuffer.wrap(data));
        }

    }
}

class DaemonClientHandler implements IoHandler {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void sessionCreated(IoSession session) throws Exception {

    }

    @Override
    public void sessionOpened(IoSession session) throws Exception {

    }

    @Override
    public void sessionClosed(IoSession session) throws Exception {

    }

    @Override
    public void sessionIdle(IoSession session, IdleStatus status) throws Exception {

    }

    @Override
    public void exceptionCaught(IoSession session, Throwable cause) throws Exception {

    }

    @Override
    public void messageReceived(IoSession session, Object message) throws Exception {
        logger.debug("client接受信息:" + message.toString());
    }

    @Override
    public void messageSent(IoSession session, Object message) throws Exception {
        logger.debug("client发送信息:" + message.toString());
    }

}