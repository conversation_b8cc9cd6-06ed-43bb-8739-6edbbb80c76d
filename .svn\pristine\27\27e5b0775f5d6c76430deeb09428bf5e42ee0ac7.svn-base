<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld"%>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld"%>

<script src="/adHoc/resources/data/js/vue.js"></script>
<link rel="stylesheet" type="text/css" href="/adHoc/resources/data/css/element-ui.css"/>
<script src="/adHoc/resources/data/js/element-ui.js"></script>

<link rel="stylesheet" href="/adHoc/resources/vueComponent/signalTimingSlider/signalTimingSlider.css">
<link rel="stylesheet" href="/adHoc/resources/vueComponent/sketchMap/sketchMap.css">
<script src="/adHoc/resources/vueComponent/utils.js"></script>
<script src="/adHoc/resources/vueComponent/signalTimingSlider/signalTimingSlider.js"></script>
<script src="/adHoc/resources/vueComponent/sketchMap/sketchMap.js"></script>

<div class="flexWrapper">
    <layout:default title="信号机-详细配置">
        <ul class="nav nav-tabs" style="margin-bottom: 0px;">
            <li><a href="${ctx}/signal/tblCollector/list?condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}&condition.model=15,16">信号机列表</a></li>
            <li><a href="#" onclick="editAdHoc()">修改信号机</a></li>
            <li class="active"><a href="#">详细配置</a></li>
        </ul>
        <form id="searchForm" style="display: none">
            <input id="dataEntityId" name="dataEntity.id" value=""/>
            <input id="conditionModel" name="condition.model" value="15"/>
        </form>
        <script>
            function editAdHoc(){
                $("#dataEntityId").val(new URLSearchParams(window.location.search).get('id'));
                $("#searchForm").attr("action","${ctx}/signal/tblCollector/form");
                $("#searchForm").submit();
                return false;
            }

            function clickOpenClose(leftIsShown) {
                if(parent.$('#left').css('display') == (leftIsShown ? 'block' : 'none'))
                    parent.$("#openClose").click();
            }

            window.addEventListener('beforeunload', _ => {
                clickOpenClose(false)
            })
        </script>
    </layout:default>

    <div id="adHocDetailConfig" class="adHocDetailConfigWrapper" v-cloak>
        <div class="sketchMapDiv">
            <sketch-map ref="sketchMap" :sketch-map-image="adHocCollectorInfo.pictureUrl"
                :component-library-title="'信号灯类型'"
                :component-library="lightTypeDict"
                :component-library-is-shown="false"
                @sketch-map-drop="onSketchMapDrop">
                <template slot="markedComponent" slot-scope="scope">
                    <div class="markedComponentWrapper">
                        <img draggable="false" :src="'${ctxStatic}/image/adHoc/sketchMapIcon/' + scope.component.remarks + '.png'">
                    </div>
                </template>
                <template slot="libraryComponent" slot-scope="scope">
                    <div class="libraryComponentWrapper">
                        <img draggable="false" :src="'${ctxStatic}/image/adHoc/sketchMapIcon/' + scope.component.remarks + '.png'">
                    </div>
                </template>
            </sketch-map>
        </div>
        <el-tabs v-model="activeTab" tab-position="right" type="border-card" class="configTabs" closable
                 @tab-click="onTabClick" @tab-remove="onTabRemoveBtnClick">
            <!--
            <el-tab-pane label="灯组配置" name="netWorkTab" style="padding: 0 10px;">
                <div class="scanStartPanel">
                    <div class="scanStartBtn" :class="scanStatus ? 'scanningBtn' : ''" v-if="scanStatus < 2" @click="onScanBtnClick">
                        {{scanStatus ? '正在' : '开始'}}扫描
                    </div>
                    <div v-else>
                        <el-table :data="scanResultTableData" stripe :default-sort="{prop: 'lightNumber', order: 'ascending'}">
                            <el-table-column sortable :align="'center'" prop="lightNumber" label="灯号" width="80">
                            </el-table-column>
                            <el-table-column sortable :align="'center'" prop="address" label="MAC地址">
                                <template slot-scope="scope">
                                    {{scope.row.address}}
                                </template>
                            </el-table-column>
                            <el-table-column sortable :align="'center'" prop="lightType" label="灯类型">
                                <template slot-scope="scope">
                                    {{lightTypeDict.find(e => e.value == scope.row.lightType).label}}
                                </template>
                            </el-table-column>
                            <el-table-column sortable :align="'center'" label="RSSI" width="90">
                                <template slot-scope="scope">
                                    {{'-'}}
                                </template>
                            </el-table-column>
                            <el-table-column sortable :align="'center'" label="GPS" width="90">
                                <template slot-scope="scope">
                                    {{'-'}}
                                </template>
                            </el-table-column>
                            <el-table-column fixed="right" :align="'center'" label="操作" width="120">
                                <template slot-scope="scope">
                                    <div class="scanTableBtnGroup">
                                        <i class="el-icon-aim" :class="scope.row.isActive ? 'isActive' : ''" title="亮灯寻址" @click="onSearchLightBtnClick(scope.row.id)"></i>
                                        <i class="el-icon-add-location" v-if="scope.row.xShaft < 0 || scope.row.yShaft < 0" title="地图标点" @click="onLocationBtnClick(scope.row.lightNumber)"></i>
                                        <i class="el-icon-delete-location" title="删除标点" v-else></i>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </el-tab-pane>
            -->
            <el-tab-pane label="快速配置" name="fastTab" style="padding: 0 10px;">
                <div class="fastPanel">
                    <el-button type="info" @click="onFastConfigBtnClick(1)">一键关灯</el-button>
                    <el-button type="danger" @click="onFastConfigBtnClick(2)">一键全红</el-button>
                    <el-button type="warning" @click="onFastConfigBtnClick(3)">一键全黄</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane v-for="(scheme, i) in schemes" :key="'schemePane_' + scheme.schemeNo + '_' + i"
                         :name="'schemePane_' + scheme.schemeNo" :label="'方案0' + scheme.schemeNo" class="schemePane">
                <div class="signalTimingDivsWrapper">
                    <el-dialog title="方案参数" :visible.sync="schemeDialogsVisible[i]" width="25%">
                        <el-form :model="scheme" label-width="100px" label-position="left" size="medium" class="schemeTimingForm">
                            <el-form-item label="周期总长" prop="cycleTotalLength">
                                <el-input v-model.number="scheme.cycleTotalLength">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="黄灯时长" prop="yellowLight">
                                <el-input v-model.number="scheme.yellowLight">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="全红时长" prop="wholeRed">
                                <el-input v-model.number="scheme.wholeRed">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
<%--                            <el-form-item label="安全绿时" prop="safeGreen">--%>
<%--                                <el-input v-model.number="scheme.safeGreen">--%>
<%--                                    <template slot="append">秒</template>--%>
<%--                                </el-input>--%>
<%--                            </el-form-item>--%>
                            <el-form-item label="机动绿闪" prop="motoVehiGreenFlash">
                                <el-input v-model.number="scheme.motoVehiGreenFlash">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="最大绿长" prop="maxGreen">
                                <el-input v-model.number="scheme.maxGreen">
                                    <template slot="append">秒</template>
                                </el-input>
                            </el-form-item>
                        </el-form>
                        <span slot="footer">
                            <el-button type="primary" @click="switchSchemeDialogsVisible(i, false)">确 定</el-button>
                        </span>
                    </el-dialog>
                    <div class="signalTimingDiv" v-for="(light, j) in scheme.lightParam" :key="'timingSlider_' + i + '_' + j">
<%--                    <span :class="'signalTimingDivNo ' + (checkedSignals.indexOf(j + 1) < 0 ? '' : 'checkedSignalsTimingDivNo')"--%>
<%--                          @click="onSignalTimingDivNoChecked(j + 1)">--%>
<%--                        {{ checkedSignals.indexOf(j + 1) < 0 ? j + 1 : "✓"}}--%>
<%--                    </span>--%>
                        <div :class="'signalTimingDivHeader ' + (checkedSignals.others.indexOf(light.lightNumber) < 0 ? '' : 'checkedSignalsTimingDivHeader')"
                             @click="onSignalTimingDivNoChecked(light.lightNumber)">
                            <div>
                                <img v-if="checkedSignals.others.indexOf(light.lightNumber) < 0" :src="'${ctxStatic}/image/adHoc/simpleIcon/' + lightTypeDict.find(e => e.value == light.lightType).remarks + '.png'">
                                <i v-else class="el-icon-check"></i>
                            </div>
                            <div>{{light.lightNumber}}</div>
                        </div>
                        <el-divider direction="vertical"></el-divider>
                        <signal-timing-slider :green-start-point.sync="light.greenStartTime"
                            :green-duration.number.sync="light.frequentGreen" :yellow-duration="scheme.yellowLight"
                            :all-red-duration="scheme.wholeRed" :max-green="scheme.maxGreen"
                            :cycle-duration="scheme.cycleTotalLength" @slidden="onSignalTimingSlidden(i, light.lightNumber, $event)"
                            @click.native="checkedSignals.target = light.lightNumber">
                        </signal-timing-slider>
                        <el-divider direction="vertical"></el-divider>
                        <i :class="'el-icon-arrow-right collapseArrow ' + (checkedCollapsePanel == light.lightNumber ? 'collapseArrowTranslation' : '')"
                           @click="onCollapseArrowClick(light.lightNumber)"></i>
                        <el-collapse-transition>
                            <div v-show="checkedCollapsePanel == light.lightNumber" class="signalTimingFormWrapper">
                                <el-form :model="light" label-width="3rem" hide-required-asterisk class="signalTimingForm">
                                    <div v-for="(paramGroup, k) in lightParamTemplate" :key="'lightPara_' + i + '_' + j +  '_' + k" class="formItemRow">
                                        <el-form-item v-for="(p, l) in paramGroup" :key="'lightPara_' + i + '_' + j +  '_' + k +  '_' + l"  :label="p.label" prop="">
                                            <el-input v-model.number="light[p.valueName]" size="mini" @input="onSignalTimingFormInputChange(i, light.lightNumber, p.valueName, $event)">
                                                <template slot="append">秒</template>
                                            </el-input>
                                        </el-form-item>
                                    </div>
                                </el-form>
                            </div>
                        </el-collapse-transition>
                        <el-divider></el-divider>
                    </div>
                </div>
                <div class="schemesTimingDiv">

                    <el-button-group>
                        <el-button type="primary" @click="updateSchemeInfo(scheme.schemeNo)" v-if="${fns:getUser().admin}" class="invisibleElement">保存</el-button>
                        <el-button type="primary" @click="putSchemeInfo(scheme.schemeNo)">下放</el-button>
                        <el-button type="primary" @click="onSchemeStartBtnClick(scheme.schemeNo)">启动</el-button>
                        <el-button @click="getSchemeInfo(scheme.schemeNo)">重置</el-button>
                    </el-button-group>
                    <el-button type="primary" icon="el-icon-setting" @click="switchSchemeDialogsVisible(i, true)">方案参数</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane name="tabAddBtn" label="+"></el-tab-pane>
        </el-tabs>
        <i class="el-icon-s-tools gloablSettingBtn" @click="onGlobalSettingBtnClick"></i>
        <el-dialog title="全局设置" :visible.sync="globalSettingDialogVisible" width="25%">
            <el-form :model="adHocCollectorInfo" label-width="120px" label-position="left" size="medium" class="schemeTimingForm">
                <el-divider>通讯参数</el-divider>
                <el-form-item label="心跳包间隔" prop="heartBeatInterval">
                    <el-input v-model.number="adHocCollectorInfo.heartBeatInterval">
                        <template slot="append">秒</template>
                    </el-input> 
                </el-form-item>
                <el-form-item label="数据包间隔" prop="dataInterval">
                    <el-input v-model.number="adHocCollectorInfo.dataInterval">
                        <template slot="append">秒</template>
                    </el-input> 
                </el-form-item>
                <el-divider>调光参数</el-divider>
                <el-form-item label="开始时间" prop="dimmingStartTime">
                    <el-time-picker v-model="adHocCollectorInfo.dimmingStartTime" placeholder="开始时间" :clearable="false"></el-time-picker>
                </el-form-item>
                <el-form-item label="结束时间" prop="dimmingEndTime">
                    <el-time-picker v-model="adHocCollectorInfo.dimmingEndTime" placeholder="结束时间" :clearable="false"></el-time-picker>
                </el-form-item>
                <el-form-item label="调光控制" prop="dimmingControlStatus">
                    <div class="flexAlignCenterWrapper">
                        <el-switch v-model="adHocCollectorInfo.dimmingControlStatus"></el-switch>
                    </div>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="onGlobalSettingPutBtnClick">下放</el-button>
                <el-button @click="onGlobalSettingResetBtnClick">重置</el-button>
            </span>
        </el-dialog>

    </div>
</div>


<script type="module">
    let signalTimingSlider = await assembleVueComponent('signalTimingSlider/signalTimingSlider.html', signalTimingSliderScript);
    let sketchMap = await assembleVueComponent('sketchMap/sketchMap.html', sketchMapScript);
    let vm = new Vue({
        el: '#adHocDetailConfig',
        components: { signalTimingSlider, sketchMap },
        computed: {
            schemeSelectOptions() {
                let result = [{label: '自定义方案', options: []}, {label: '特殊方案', options: []}]
                for (const s of this.schemes)
                    result[0].options.push({value: s.schemeNo, label: '方案0' + s.schemeNo})
                for (const s of this.specialSchemes)
                    result[1].options.push({value: s.schemeNo, label: s.schemeName})
                return result
            }
        },
        mounted() {
            this.adHocId = new URLSearchParams(window.location.search).get('id');
            this.getAdHocCollectorInfo();
            this.getSchemeInfo();
            this.getLightTypeDict();
        },
        data() {
            return {
                adHocId: '',
                adHocName: '未知信号机',
                adHocCollectorInfo: {},
                activeTab: 'schemePane_1',
                scanStatus: 0,
                scanResultTableData: [],
                schemes: [],
                presetsSchemes: {
                    id: '',
                    schemeNo: 0,
                    motoVehiGreenFlash: 1,
                    safeGreen: 15,
                    yellowLight: 3,
                    wholeRed: 3,
                    maxGreen: 50,
                    cycleTotalLength: 200
                },
                
                checkedSignals: {target: 0, others: []},
                checkedCollapsePanel: 0,
                
                schemeDialogsVisible: [false, false, false, false],
                globalSettingDialogVisible: false,

                lightTypeDict: [],
                lightParamTemplate: [[
                    {label: '起点', valueName: 'greenStartTime'},
                    {label: '绿长', valueName: 'frequentGreen'}
                ], [
                    {label: '红计时', valueName: 'redTime'},
                    {label: '绿计时', valueName: 'greenTime'},
                ]]
            }
        },
        methods: {
            async getAdHocCollectorInfo() {
                try {
                    let {data: collectorInfo} = await jsonFetch("${path}/adHoc/a/signal/tblCollector/get?id=" + this.adHocId, "post", null, "无法获取信号机信息")
                    let {data: alarmConfig} = await jsonFetch("${path}/adHoc/a/signal/tblAlarmConfig/getAlarmConfig?collectorId=" + this.adHocId, "post", null, "无法获取通讯配置")
                    collectorInfo.heartBeatInterval = alarmConfig.stateInterval
                    collectorInfo.dataInterval = alarmConfig.dataInterval
                    this.adHocCollectorInfo = collectorInfo
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async getSchemeInfo(schemeNo='') {
                try {
                    let {data: result} = await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/getSchemeInfo?id=" + this.adHocId + "&schemeNo=" + schemeNo, "post", null, "无法获取方案信息")
                    let schemeIndex = this.schemes.findIndex(e => e.schemeNo == schemeNo)

                    this.preProcessReceivedSchemeData(result??[])   

                    if(schemeNo) 
                        this.$set(this.schemes, schemeIndex == -1 ? this.schemes.length : schemeIndex, result[0])
                    else
                        this.$set(this, 'schemes', result??[])
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async getLightTypeDict() {
                let {data: lightTypeDict} = await jsonFetch("${path}/adHoc/common/getDictByType?type=light_type", "post", null, "获取灯类型字典失败")
                this.lightTypeDict = lightTypeDict
            },  
            async addNewScheme() {  
                if(this.schemes.length >= 4) 
                    return ELEMENT.Message.warning('方案数量已达上限，无法继续添加。')
                
                try {
                    let schemeNo = this.schemes.length + 1
                    let result = await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/addSchemeInfo", "post",
                        this.preProcessPostSchemeData(schemeNo, 'add'), "无法添加新方案")
                    await this.getSchemeInfo(schemeNo)
                    this.activeTab = 'schemePane_' + schemeNo
                    ELEMENT.Message.success('方案添加成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },  
            async updateSchemeInfo(schemeNo) {
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/updateSchemeInfo", "post",
                        this.preProcessPostSchemeData(schemeNo, 'update'), "无法更新方案信息")
                    await this.getSchemeInfo(schemeNo)
                    ELEMENT.Message.success('方案更新成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async putSchemeInfo(schemeNo) {
                try {
                    await this.updateSchemeInfo(schemeNo)
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/putSchemeInfo", "post",
                        this.preProcessPostSchemeData(schemeNo, 'put'), "方案下放失败")
                    ELEMENT.Message.success('方案下放成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }   
            },
            async onSchemeStartBtnClick(schemeNo) {
                try {
                    ELEMENT.MessageBox.confirm('是否启动方案?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                        beforeClose: async (action, instance, done) => {
                            if(action == 'confirm') {
                                try {
                                    instance.confirmButtonLoading = true
                                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/putPortableSchemeLevelInfo", "post",
                                        {id: this.adHocId, schemeNo: schemeNo}, "方案启动失败")
                                    ELEMENT.Message.success('方案已启动')
                                } catch (e) {
                                    ELEMENT.Message.error(e.message)
                                } finally {
                                    instance.confirmButtonLoading = false
                                }
                            }
                            done()
                        }
                    })
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            onGlobalSettingBtnClick() {
                this.globalSettingDialogVisible = true
            },
            async onTabClick(e) {
                switch (e.name) {
                    case "netWorkTab":
                        break;
                    case "tabAddBtn":
                        this.addNewScheme()
                        break;
                    default:
                        this.getSchemeInfo(e.name.split('_')[1])
                        break;
                }
            },
            async onTabRemoveBtnClick(paneName) {
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/deleteSchemeInfo?id=" + this.adHocId
                        + "&schemeNo=" + paneName.split('_')[1], "post", null, "方案删除失败")
                    await this.getSchemeInfo()
                    
                    this.activeTab = 'schemePane_1'
                    ELEMENT.Message.success('方案删除成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async onScanBtnClick() {
                this.scanStatus++;
                window.setTimeout(_=> this.scanStatus++, 2000)
                try {
                    let {data: lightInfo} = await jsonFetch("${path}/adHoc/a/signal/adHOCConfig/getLight?id=" + this.adHocId, "post", null, "获取灯信息失败")
                    console.log(lightInfo)
                    this.scanResultTableData = lightInfo
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            async onSearchLightBtnClick(lightId) {
                let lightInfo = this.scanResultTableData.find(e => e.id == lightId)
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putAdHOCFollowControllerInfo?id=" + this.adHocId +
                        "&followLightAdress=" + lightInfo.address + "&status=" + +!lightInfo.isActive, "post", null, "无法发送亮灯寻址指令")
                    this.$set(lightInfo, 'isActive', !lightInfo.isActive)
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            onLocationBtnClick(lightNumber) {
                this.$refs['sketchMap'].highlightElements(window.top.document, document.getElementsByClassName('componentLibraryWrapper'))
            },
            onSketchMapDrop(lightNo) {
                console.log(lightNo)
            },
            async onFastConfigBtnClick(configType) {
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putOnClick?id=" + this.adHocId +
                        "&onclickParameter=" + configType, "post", null, "无法发送一键配置指令")
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },
            onSignalTimingDivNoChecked(signalNo) {
                let index = this.checkedSignals.others.indexOf(signalNo)
                if(index < 0)
                    this.checkedSignals.others.push(signalNo)
                else
                    this.checkedSignals.others.splice(index, 1)
            },
            onSignalTimingSlidden(schemeNo, lightNo, greenParams) {
                for (const light of this.schemes[schemeNo].lightParam.filter(e => this.checkedSignals.others.includes(e.lightNumber) && e.lightNumber != this.checkedSignals.target)) {
                    light.greenStartTime = greenParams[0]
                    light.frequentGreen = greenParams[1] - greenParams[0]
                }
                // console.log(JSON.stringify(this.schemes[schemeNo].lightParam[0]) )
            },
            onCollapseArrowClick(signalNo) {
                this.checkedCollapsePanel = this.checkedCollapsePanel == signalNo ? 0 : signalNo
            },
            onSignalTimingFormInputChange(schemeNo, lightNo, paramName, value) {
                for (const light of this.schemes[schemeNo].lightParam.filter(e => this.checkedSignals.others.includes(e.lightNumber) && e.lightNumber != lightNo)) {
                    light[paramName] = +value
                }
            },

            switchSchemeDialogsVisible(index, isShown) {
                this.$set(this.schemeDialogsVisible, index, isShown)
            },

            preProcessPostSchemeData(schemeNo, requestType) {
                let result 

                if(requestType == 'add') {
                    result = JSON.parse(JSON.stringify(this.presetsSchemes))
                    result.schemeNo = schemeNo
                } else {
                    result = JSON.parse(JSON.stringify(this.schemes.find(e => e.schemeNo == schemeNo)))
                    this.renameObjectKey(result, 'lightParam', 'lightValueBeans')
                    for (const e of result.lightValueBeans) e.lightStatus = 0
                    if(requestType == 'put') delete result.maxGreen
                }
                delete result.collectorId
                result.id = this.adHocId
                return result
            },

            preProcessReceivedSchemeData(schemes){
                for(const scheme of schemes) {
                    this.renameObjectKey(scheme, 'alterableLightValueBeans', 'lightParam')
                    scheme.lightParam.sort((a, b) => a.lightNumber - b.lightNumber)
                }
                schemes = convertString2Numbers(schemes)
                schemes.sort((a, b) => a.schemeNo - b.schemeNo)
            },

            async onGlobalSettingPutBtnClick() {
                try {
                    await jsonFetch("${path}/adHoc/a/signal/adHOCPutConfig/putAdHOCHeartBeat", "post", {
                        id: this.adHocId,
                        heartBeatInterval: this.adHocCollectorInfo.heartBeatInterval,
                        dataInterval: this.adHocCollectorInfo.dataInterval,
                        statusPartInvalidInterval: 0
                    }, "通讯配置下放失败")
                    await jsonFetch("${path}/adHoc/a/signal/adHOCScheme/putDimming", "post", {
                        id: this.adHocId,
                        dimmingParam: {
                            startTime: new Date(this.adHocCollectorInfo.dimmingStartTime).toLocaleTimeString(),
                            endTime: new Date(this.adHocCollectorInfo.dimmingEndTime).toLocaleTimeString(),
                            controlStatus: +this.adHocCollectorInfo.dimmingControlStatus 
                        }
                    }, "调光配置下放失败")
                    ELEMENT.Message.success('全局配置下放成功')
                } catch (e) {
                    ELEMENT.Message.error(e.message)
                }
            },

            onGlobalSettingResetBtnClick() {
                this.getAdHocCollectorInfo()
            },

            renameObjectKey(obj, oldKey, newKey) {
                obj[newKey] = obj[oldKey]
                delete obj[oldKey]
            },

            underline2Hump(name) {
                let result = ''
                let words = name.split('_')
                for (let i = 1; i < words.length; i++)
                    result += words[i].slice(0,1).toUpperCase() + words[i].slice(1, words[i].length)
                return words[0] + result
            },
            time2Date(time) {
                let temp = time.split(':')
                return new Date(0, 0, 0, temp[0], temp[1], temp[2])
            },
            dateArray2TimeStringArray(dateArray) {
                let timeStringArray = []
                for (const e of dateArray)
                    timeStringArray.push(e.toLocaleTimeString())
                return timeStringArray
            },
            isInInterval(start, end, value) {
                return value >= start && value <= end
            },
            goBack() {
                window.history.back()
            },
            aaa(e) {
                console.log(e)
            }

        }
    })

</script>

<style>
    html {
        overflow: hidden !important;
    }

    .flexWrapper {
        display: flex;
        flex-direction: column;
        height: 100vh;
    }

    .adHocDetailConfigWrapper {
        display: flex;
        flex: 1;
        height: 0;
    }

    .sketchMapDiv {
        display: flex;
        flex: 1;
        padding: 5px;
    }

    .libraryComponentWrapper {
        background-color: #F00;
    }

    .configTabs {
        width: 50%;
        flex: 1;
    }

    .configTabs .el-tabs__header.is-right {
        margin-left: 0;
    }

    .configTabs .el-tabs__nav-wrap.is-scrollable.is-right {
        padding: 0;
    }

    .configTabs .el-tabs__nav-prev, .configTabs .el-tabs__nav-next {
        display: none;
    }

    .configTabs .el-tabs__item.is-right{
        width: 1rem;
        height: auto;
        padding: 5px 20px;
        white-space: break-spaces;
        line-height: 1.2rem;
        border-bottom: 2px solid #dfdfdf !important;
        display: flex;
        flex-direction: column;
        align-items: center;
        user-select: none;
    }

    .configTabs .el-tabs__item.is-right .el-icon-close {
        margin: 0px;
    }

    .configTabs .el-tabs__item .el-icon-close {
        display: none;
    }

    .configTabs .el-tabs__item:first-child .el-icon-close, .configTabs .el-tabs__item:nth-child(1) .el-icon-close, .configTabs .el-tabs__item:last-child .el-icon-close {
        display: none !important;
    }

    .configTabs .el-tabs__item.is-active .el-icon-close {
        display: inline-block;
    }

    .configTabs .el-tabs__content {
        height: 100%;
        padding: 0;
    }

    .configTabs .el-tab-pane {
        overflow: auto;
        height: calc(100% - 10px);
        padding-top: 10px;
        scrollbar-width: thin;
    }

    .configTabs .el-tab-pane::-webkit-scrollbar {
        width: 5px;
    }

    .configTabs .el-tab-pane::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 5px;
    }

    .scanStartPanel, .fastPanel {
        width: 100%;
        height: 100%;
    }

    .scanStartBtn {
        border: lightgrey 1px solid;
        border-radius: 100px;
        width: 10rem;
        height: 10rem;
        font-size: 23px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background-image: linear-gradient(#409effD0 , #409EFF);
        color: white;
        cursor: pointer;
        user-select: none;
    }

    .scanningBtn::before {
        content: "";
        width: 100%;
        height: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        background-image: linear-gradient(225deg, #FFFFFF00 50%, #FFFFFF 100%);
        transform-origin: left top;
        animation: scanRotate 2s infinite linear;
    }

    @keyframes scanRotate {
        from { transform: rotate(-180deg); }
        to { transform: rotate(180deg); }
    }

    .scanStartPanel .el-table__body-wrapper {
        scrollbar-width: thin;
    }

    .scanStartPanel .el-table__body-wrapper::-webkit-scrollbar {
        height: 8px;
    }

    .scanStartPanel .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 5px;
    }

    .scanStartPanel .el-table .caret-wrapper {
        width: 0px;
    }

    .scanStartPanel .el-table .cell {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .scanTableBtnGroup {
        display: flex;
        justify-content: space-evenly;
        font-size: 25px;
        color: #909399;
        user-select: none;
    }

    .scanTableBtnGroup>i {
        cursor: pointer;
    }

    .scanTableBtnGroup>i.isActive {
        color: #E6A23C !important;
    }

    .scanTableBtnGroup>i:hover {
        color: #409EFF;
    }

    .schemePane {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .signalTimingDivsWrapper {
        overflow: auto;
        flex: 1;
    }

    .signalTimingDiv {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 0 5px 0 10px;
    }

    .signalTimingDivHeader, .signalTimingDivHeader div {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .signalTimingDivHeader {
        margin: 0 5px;
    }

    .signalTimingDivHeader div {
        border: 2px solid #606266;
        width: 1.5rem;
        height: 1.5rem;
        font-size: 18px;
        font-weight: bolder;
        color: #606266;
        cursor: pointer;
        user-select: none;
        -webkit-user-drag: none;
    }

    .signalTimingDivHeader div:first-child {
        background-color: #606266;
        color: white;
        font-weight: bolder !important;
        border-radius: 10px 0 0 10px;
        padding-left: 2px;
    }

    .signalTimingDivHeader div:nth-child(2) {
        border-radius: 0 10px 10px 0;
    }

    .checkedSignalsTimingDivHeader div {
        color: #409EFF;
        border-color: #409EFF;
    }

    .checkedSignalsTimingDivHeader div:first-child {
        background-color: #409EFF;
        padding: 0 1px;
    }

    .signalTimingDiv .el-divider.el-divider--vertical {
        height: 90px ;
    }

    .signalTimingSlider {
        flex: 1;
        padding: 0 5px;
    }

    .collapseArrow {
        font-size: 18px;
        margin: 0 5px;
        cursor: pointer;
        transform: rotateZ(0deg);
        transition: transform 0.2s;
    }

    .collapseArrowTranslation {
        font-size: 18px;
        margin: 0 5px;
        cursor: pointer;
        transform: rotateZ(90deg);
        transition: transform 0.2s;
    }

    .signalTimingFormWrapper {
        width: 100%;
    }

    .signalTimingForm {
        margin: 0;
    }

    .signalTimingForm .formItemRow {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
    }

    .signalTimingForm .el-form-item {
        display: flex;
        align-items: center;
        margin-bottom: 0px;
    }

    .signalTimingForm .el-form-item__label {
        box-sizing: content-box;
        text-align-last: justify;
    }

    .signalTimingForm .el-form-item__content {
        margin-left: 5px !important;
    }

    .signalTimingForm .el-input {
        width: 150px;
    }

    .signalTimingForm input[type="text"], .schemeTimingForm input[type="text"] {
        margin: 0px;
        text-align: center;
    }

    .signalTimingForm .el-input-group__append {
        padding: 0 10px;
    }

    .signalTimingDiv .el-divider--horizontal {
        margin: 12px 0;
    }

    .signalTimingDiv:last-child .el-divider--horizontal {
        margin: 12px 0 0 0;
        height: 0;
    }

    .schemesTimingDiv {
        position: sticky;
        position: -webkit-sticky;
        bottom: 0;
        background-color: #FFFFFF;
        z-index: 1002;
        display: flex;
        flex-direction: row-reverse;
        border-top: #dfdfdf 1px solid;
        padding: 10px;
    }

    .schemeTimingForm {
        width: 90%;
        margin: auto;
    }

    .schemeTimingForm .el-form-item {
        height: 36px;
    }

    .schemeTimingForm .el-form-item__label{
        font-size: 16px;
    }

    .schemesTimingDiv .el-button {
        margin: 0 10px 0 0;
    }

    .gloablSettingBtn {
        position: fixed;
        bottom: 3px;
        right: 3px;
        z-index: 1003;
        font-size: 26px;
        border: 2px solid #909399;
        border-radius: 30px;
        padding: 2px;
        color: #909399;
        margin-bottom: 5px;
        cursor: pointer;
        opacity: 0.65;
    }

    #adHocDetailConfig .el-button {
        padding: 8px 12px;
    }
    #adHocDetailConfig .el-dialog {
        min-width: 400px;
    }
    #adHocDetailConfig .el-dialog__body {
        padding: 5px 20px;
    }
    #adHocDetailConfig .el-dialog__footer {
        padding-top: 0px;
    }
    .flexAlignCenterWrapper {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .invisibleElement {
        display: none;
    }

    [v-cloak] {
        display: none;
    }
</style>
